{"version": 3, "file": "dmg.js", "sourceRoot": "", "sources": ["../src/dmg.ts"], "names": [], "mappings": ";;;AAAA,qDAAoD;AACpD,0EAAsF;AAEtF,6GAA0F;AAC1F,oEAA6E;AAC7E,gEAAoE;AACpE,+CAAyI;AAEzI,4CAA2E;AAC3E,uCAA+B;AAC/B,6BAA4B;AAE5B,6CAA8C;AAC9C,uCAAyF;AACzF,2BAA4C;AAE5C,MAAa,SAAU,SAAQ,wBAAM;IAGnC,YACmB,QAAqB,EAC7B,MAAc;QAEvB,KAAK,CAAC,KAAK,CAAC,CAAA;QAHK,aAAQ,GAAR,QAAQ,CAAa;QAC7B,WAAM,GAAN,MAAM,CAAQ;QAJhB,YAAO,GAAe,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAO9E,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,OAAe,EAAE,IAAU;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,uDAAuD;QACvD,MAAM,YAAY,GAAG,QAAQ,CAAC,yBAAyB,CACrD,IAAI,CAAC,OAAO,EACZ,KAAK,EACL,IAAI,EACJ,iBAAiB,GAAG,CAAC,QAAQ,CAAC,4BAA4B,CAAC,kBAAkB,IAAI,YAAY,CAAC,GAAG,iBAAiB,EAClH,IAAI,EACJ,QAAQ,CAAC,4BAA4B,CAAC,WAAW,CAClD,CAAA;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QACzD,MAAM,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC3C,qBAAqB,EAAE,KAAK;YAC5B,IAAI,EAAE,YAAY;YAClB,IAAI;SACL,CAAC,CAAA;QAEF,MAAM,UAAU,GAAG,IAAA,2BAAgB,EAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;QAErF,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,MAAM,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,UAAU,CAAC,CAAA;QAE7F,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACpD,oEAAoE;QACpE,MAAM,cAAc,GAAG,aAAa,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,6BAA6B,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAC5J,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,aAAa,EAAE,cAAc,CAAC,CAAA;QACjH,MAAM,kBAAkB,GAAG,SAAS,GAAG,GAAG,GAAG,SAAS,CAAA;QACtD,MAAM,IAAA,mBAAI,EAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,kBAAkB,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC,CAAA;QAElF,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;QACpD,IAAI,MAAM,IAAA,WAAM,EAAC,UAAU,CAAC,EAAE,CAAC;YAC7B,kBAAG,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,EAAE,gCAAgC,CAAC,CAAA;YAC3D,MAAM,IAAA,gBAAM,EAAC,UAAU,CAAC,CAAA;QAC1B,CAAC;QAED,IAAI,CAAC,CAAC,MAAM,IAAA,0BAAgB,EAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;YACtH,OAAM;QACR,CAAC;QAED,yKAAyK;QACzK,MAAM,IAAI,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,CAAC,MAAO,EAAE,IAAI,EAAE,YAAY,CAAC,CAAA;QAC9F,IAAI,aAAa,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,GAAG,EAAE,CAAC,CAAA;QAC/F,CAAC;QACD,MAAM,IAAA,oBAAK,EAAC,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACzC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,QAAQ,CAAC,IAAA,YAAY,GAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC;YACpF,MAAM,IAAA,mBAAI,EAAC,SAAS,EAAE,WAAW,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAA;QAC9E,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,IAAA,4BAAe,EAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;QACjE,IAAI,QAAQ,CAAC,eAAe,CAAC,uBAAuB,IAAI,IAAI,EAAE,CAAC;YAC7D,MAAM,QAAQ,CAAC,eAAe,CAAC,uBAAuB,CAAC,EAAE,WAAW,EAAE,CAAC,CAAA;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;QAClC,CAAC;QAED,MAAM,gBAAgB,GAAG,QAAQ,CAAC,uBAAuB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;QAC9E,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,IAAA,8CAAc,EAAC,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAA;QACvI,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAC7C,IAAI,EAAE,YAAY;YAClB,gBAAgB;YAChB,MAAM,EAAE,IAAI;YACZ,IAAI;YACJ,QAAQ;YACR,iBAAiB,EAAE,UAAU,IAAI,IAAI;YACrC,UAAU;SACX,CAAC,CAAA;IACJ,CAAC;IAEO,KAAK,CAAC,OAAO,CAAC,YAAoB;QACxC,IAAI,CAAC,IAAA,2BAAa,EAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAM;QACR,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,SAAS,GAAG,QAAQ,CAAC,4BAA4B,CAAC,QAAQ,CAAA;QAChE,qCAAqC;QACrC,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACvB,0EAA0E;YAC1E,OAAM;QACR,CAAC;QAED,MAAM,YAAY,GAAG,CAAC,MAAM,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,YAAY,CAAA;QACxE,MAAM,eAAe,GAAG,0BAA0B,CAAA;QAClD,IAAI,QAAQ,GAAG,MAAM,IAAA,0BAAY,EAAC,eAAe,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;QAC3E,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,QAAQ,GAAG,MAAM,IAAA,0BAAY,EAAC,eAAe,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;YACvE,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;gBACrB,OAAM;YACR,CAAC;QACH,CAAC;QAED,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAK,CAAC,CAAA;QACvC,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;QACvC,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QACvB,MAAM,IAAA,mBAAI,EAAC,UAAU,EAAE,IAAI,CAAC,CAAA;IAC9B,CAAC;IAED,iBAAiB,CAAC,IAAU,EAAE,MAAsB;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAA;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,kBAAkB,IAAI,OAAO,CAAC,OAAO,CAAA;QACrG,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,WAAW,CAAC,CAAA;QAE9F,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,OAAO,GAAG,OAAO,CAAC,eAAe,IAAI,YAAY,GAAG,UAAU,EAAE,CAAA;QAClE,CAAC;QAED,OAAO,MAAM;aACV,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC;aAChC,OAAO,CAAC,mBAAmB,EAAE,YAAY,CAAC;aAC1C,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,OAAO,CAAC;aACxC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC;aAClC,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC,WAAW,CAAC,CAAA;IACrD,CAAC;IAED,iBAAiB;IACjB,KAAK,CAAC,iBAAiB;QACrB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,aAAa,GAAe,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;QACrD,IAAI,aAAa,CAAC,IAAI,IAAI,IAAI,IAAI,aAAa,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YAC9D,aAAa,CAAC,IAAI,GAAG,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAA;QACnD,CAAC;QAED,IAAI,aAAa,CAAC,IAAI,IAAI,IAAI,IAAI,IAAA,8BAAe,EAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACtE,MAAM,IAAI,wCAAyB,CAAC,8CAA8C,CAAC,CAAA;QACrF,CAAC;QAED,MAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAA;QAC3C,IAAI,aAAa,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC;YAC1C,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;gBACvB,MAAM,IAAI,wCAAyB,CAAC,qFAAqF,CAAC,CAAA;YAC5H,CAAC;QACH,CAAC;aAAM,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YAC9B,aAAa,CAAC,UAAU,GAAG,MAAM,IAAA,2BAAiB,EAAC,QAAQ,CAAC,CAAA;QAC9D,CAAC;aAAM,CAAC;YACN,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;QAC/E,CAAC;QAED,IAAI,aAAa,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;YACjC,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,IAAI,EAAE,CAAC;gBAC3D,CAAC;gBAAC,aAAqB,CAAC,MAAM,GAAG,MAAM,CAAA;YACzC,CAAC;iBAAM,IAAI,QAAQ,CAAC,WAAW,KAAK,OAAO,EAAE,CAAC;gBAC5C,aAAa,CAAC,MAAM,GAAG,MAAM,CAAA;YAC/B,CAAC;iBAAM,CAAC;gBACN,aAAa,CAAC,MAAM,GAAG,QAAQ,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAA;YAC7E,CAAC;QACH,CAAC;QAED,IAAI,aAAa,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;YACnC,aAAa,CAAC,QAAQ,GAAG;gBACvB;oBACE,CAAC,EAAE,GAAG;oBACN,CAAC,EAAE,GAAG;iBACP;gBACD;oBACE,CAAC,EAAE,GAAG;oBACN,CAAC,EAAE,GAAG;oBACN,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,eAAe;iBACtB;aACF,CAAA;QACH,CAAC;QACD,OAAO,aAAa,CAAA;IACtB,CAAC;CACF;AAlLD,8BAkLC;AAED,KAAK,UAAU,cAAc,CAAC,OAAe,EAAE,OAAe,EAAE,UAAkB;IAChF,sCAAsC;IACtC,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAA;IACzI,IAAI,kBAAG,CAAC,cAAc,EAAE,CAAC;QACvB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC1B,CAAC;IAED,IAAI,UAAU,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,mBAAmB,CAAC,CAAA;IACzD,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC7B,gFAAgF;QAChF,oEAAoE;QACpE,UAAU,GAAG,CAAC,MAAM,CAAC,CAAA;QACrB,kBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,+FAA+F,CAAC,CAAA;IACjH,CAAC;IACD,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,UAAU,CAAC,CAAA;IACpC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IACvB,uHAAuH;IACvH,oEAAoE;IACpE,MAAM,IAAA,oBAAK,EAAC,GAAG,EAAE,CAAC,IAAA,oBAAK,EAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;IACvD,OAAO,OAAO,CAAA;AAChB,CAAC;AAED,SAAS,WAAW,CAAC,IAAmB;IACtC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;IACnE,OAAO,IAAI,CAAA;AACb,CAAC;AAED,KAAK,UAAU,gBAAgB,CAAC,iBAAoC,EAAE,OAAe,EAAE,aAAyB,EAAE,cAAyC;IACzJ,MAAM,gBAAgB,GAAG,IAAI,+BAAgB,CAAC,iBAAiB,CAAC,CAAA;IAChE,gBAAgB,CAAC,OAAO,CAAC,IAAA,eAAI,EAAC,OAAO,CAAC,CAAC,CAAA;IAEvC,IAAI,aAAa,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;QAC/B,gBAAgB,CAAC,OAAO,CAAC,IAAA,eAAU,EAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;IAC1D,CAAC;IAED,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;QAC3B,gBAAgB,CAAC,OAAO,CAAC,IAAA,eAAI,EAAC,cAAc,CAAC,CAAC,CAAA;IAChD,CAAC;IAED,IAAI,MAAM,GAAG,EAAE,GAAG,IAAI,CAAA;IACtB,KAAK,MAAM,IAAI,IAAI,MAAM,gBAAgB,CAAC,UAAU,EAAE,EAAE,CAAC;QACvD,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,IAAI,CAAC,IAAI,CAAA;QACrB,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,KAAK,UAAU,YAAY,CAAC,UAAkB,EAAE,aAAyB,EAAE,QAAqB,EAAE,cAAyC;IACzI,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAAA;IACnC,MAAM,mBAAmB,GAAG,CAAC,CAAC,aAAa,CAAC,YAAY,IAAI,aAAa,CAAC,YAAY,IAAI,EAAE,IAAI,aAAa,CAAC,YAAY,IAAI,EAAE,CAAA;IAChI,MAAM,YAAY,GAAG,mBAAmB,CAAC,CAAC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAA;IAC1E,MAAM,GAAG,GAAQ;QACf,GAAG,OAAO,CAAC,GAAG;QACd,UAAU;QACV,WAAW,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,eAAe,MAAM;QACtD,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,EAAE;QACtC,YAAY;QAEZ,gBAAgB,EAAE,MAAM;KACzB,CAAA;IAED,IAAI,aAAa,CAAC,eAAe,IAAI,IAAI,IAAI,aAAa,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC;QAC9E,GAAG,CAAC,eAAe,GAAG,aAAa,CAAC,eAAe,IAAI,SAAS,CAAA;QAEhE,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,GAAG,CAAC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;YAC5D,GAAG,CAAC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;YAC5D,GAAG,CAAC,WAAW,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;YAClD,GAAG,CAAC,YAAY,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;QACtD,CAAC;IACH,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,CAAC,eAAe,CAAA;IAC5B,CAAC;IAED,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,UAAU,CAAC,CAAA;IAC5C,IAAI,aAAa,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAE,CAAC,CAAA;IACxE,CAAC;IACD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,cAAc,CAAC,CAAA;IAC3C,CAAC;IAED,MAAM,IAAI,GAAQ,MAAM,IAAA,oCAAuB,EAAC,IAAI,CAAC,CAAA;IACrD,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC;QACjC,GAAG,CAAC,WAAW,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAA;QACtD,GAAG,CAAC,YAAY,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAA;QAExD,IAAI,GAAG,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAC5B,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAA;QACnD,CAAC;QACD,IAAI,GAAG,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC;YAC7B,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAA;QACrD,CAAC;QAED,IAAI,GAAG,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YACxB,GAAG,CAAC,OAAO,GAAG,GAAG,CAAA;QACnB,CAAC;QACD,IAAI,GAAG,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YACxB,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;QACpE,CAAC;IACH,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;IAExB,MAAM,gBAAgB,GAAG,IAAI,+BAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;IAC9E,GAAG,CAAC,aAAa,GAAG,MAAM,iBAAiB,CAAC,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAA;IAClG,MAAM,gBAAgB,CAAC,UAAU,EAAE,CAAA;IACnC,MAAM,aAAa,GAAG,KAAK,EAAE,QAAgB,EAAE,EAAE;QAC/C,IAAI,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,CAAA;QACxC,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,UAAU,GAAG,CAAC,MAAM,IAAA,mBAAI,EAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;QACvD,CAAC;QACD,MAAM,IAAA,mBAAI,EAAC,UAAU,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAA,0BAAgB,GAAE,EAAE,kBAAkB,CAAC,CAAC,EAAE;YAC1E,GAAG,EAAE,IAAA,0BAAgB,GAAE;YACvB,GAAG;SACJ,CAAC,CAAA;IACJ,CAAC,CAAA;IACD,IAAI,CAAC;QACH,MAAM,aAAa,CAAC,SAAS,CAAC,CAAA;IAChC,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,MAAM,aAAa,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IACD,OAAO,QAAQ,CAAC,eAAe,CAAC,uBAAuB,IAAI,IAAI,IAAI,CAAC,CAAC,MAAM,QAAQ,CAAC,eAAe,CAAC,uBAAuB,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAA;AACvK,CAAC;AAED,KAAK,UAAU,iBAAiB,CAAC,aAAyB,EAAE,UAAkB,EAAE,QAAqB,EAAE,gBAAkC;IACvI,IAAI,MAAM,GAAG,EAAE,CAAA;IACf,KAAK,MAAM,CAAC,IAAI,aAAa,CAAC,QAAS,EAAE,CAAC;QACxC,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACnE,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,yCAAyC,EAAE,EAAE,qCAAqC,CAAC,CAAA;QACtH,CAAC;QAED,MAAM,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,eAAe,MAAM,CAAA;QACrE,MAAM,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;QACpD,MAAM,gBAAgB,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,CAAA;QAC3E,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAA;QACjB,CAAC;QACD,MAAM,IAAI,IAAI,gBAAgB,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAA;QAEnD,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACtB,gBAAgB,CAAC,OAAO,CAAC,IAAA,mBAAI,EAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,GAAG,UAAU,IAAI,SAAS,EAAE,CAAC,CAAC,CAAC,CAAA;QAClJ,CAAC;QACD,+JAA+J;aAC1J,IAAI,CAAC,IAAA,8BAAe,EAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;YAC7E,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;YACjD,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;gBACnB,kBAAG,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE,EAAE,0BAA0B,CAAC,CAAA;gBAC5E,SAAQ;YACV,CAAC;YAED,MAAM,WAAW,GAAG,GAAG,UAAU,IAAI,SAAS,EAAE,CAAA;YAChD,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,IAAA,eAAI,EAAC,MAAM,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAA,YAAO,EAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,IAAA,aAAQ,EAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAA;QACjJ,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,KAAK,UAAU,6BAA6B,CAAC,IAAY,EAAE,MAAc;IACvE,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QACrD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAA;IACxD,IAAI,MAAM,IAAA,WAAM,EAAC,UAAU,CAAC,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAA;QAC9D,MAAM,IAAA,mBAAI,EAAC,UAAU,EAAE,CAAC,gBAAgB,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAA;QAC9E,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC", "sourcesContent": ["import { DmgOptions, Target } from \"app-builder-lib\"\nimport { findIdentity, isSignAllowed } from \"app-builder-lib/out/codeSign/macCodeSign\"\nimport MacPackager from \"app-builder-lib/out/macPackager\"\nimport { createBlockmap } from \"app-builder-lib/out/targets/differentialUpdateInfoBuilder\"\nimport { executeAppBuilderAsJson } from \"app-builder-lib/out/util/appBuilder\"\nimport { sanitizeFileName } from \"app-builder-lib/out/util/filename\"\nimport { Arch, AsyncTaskManager, exec, getArchSuffix, InvalidConfigurationError, isEmptyOrSpaces, log, spawn, retry } from \"builder-util\"\nimport { CancellationToken } from \"builder-util-runtime\"\nimport { copyDir, copyFile, exists, statOrNull } from \"builder-util/out/fs\"\nimport { stat } from \"fs-extra\"\nimport * as path from \"path\"\nimport { TmpDir } from \"temp-file\"\nimport { addLicenseToDmg } from \"./dmgLicense\"\nimport { attachAndExecute, computeBackground, detach, getDmgVendorPath } from \"./dmgUtil\"\nimport { release as getOsRelease } from \"os\"\n\nexport class DmgTarget extends Target {\n  readonly options: DmgOptions = this.packager.config.dmg || Object.create(null)\n\n  constructor(\n    private readonly packager: MacPackager,\n    readonly outDir: string\n  ) {\n    super(\"dmg\")\n  }\n\n  async build(appPath: string, arch: Arch) {\n    const packager = this.packager\n    // tslint:disable-next-line:no-invalid-template-strings\n    const artifactName = packager.expandArtifactNamePattern(\n      this.options,\n      \"dmg\",\n      arch,\n      \"${productName}-\" + (packager.platformSpecificBuildOptions.bundleShortVersion || \"${version}\") + \"-${arch}.${ext}\",\n      true,\n      packager.platformSpecificBuildOptions.defaultArch\n    )\n    const artifactPath = path.join(this.outDir, artifactName)\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: \"DMG\",\n      file: artifactPath,\n      arch,\n    })\n\n    const volumeName = sanitizeFileName(this.computeVolumeName(arch, this.options.title))\n\n    const tempDmg = await createStageDmg(await packager.getTempFile(\".dmg\"), appPath, volumeName)\n\n    const specification = await this.computeDmgOptions()\n    // https://github.com/electron-userland/electron-builder/issues/2115\n    const backgroundFile = specification.background == null ? null : await transformBackgroundFileIfNeed(specification.background, packager.info.tempDirManager)\n    const finalSize = await computeAssetSize(packager.info.cancellationToken, tempDmg, specification, backgroundFile)\n    const expandingFinalSize = finalSize * 0.1 + finalSize\n    await exec(\"hdiutil\", [\"resize\", \"-size\", expandingFinalSize.toString(), tempDmg])\n\n    const volumePath = path.join(\"/Volumes\", volumeName)\n    if (await exists(volumePath)) {\n      log.debug({ volumePath }, \"unmounting previous disk image\")\n      await detach(volumePath)\n    }\n\n    if (!(await attachAndExecute(tempDmg, true, () => customizeDmg(volumePath, specification, packager, backgroundFile)))) {\n      return\n    }\n\n    // dmg file must not exist otherwise hdiutil failed (https://github.com/electron-userland/electron-builder/issues/1308#issuecomment-282847594), so, -ov must be specified\n    const args = [\"convert\", tempDmg, \"-ov\", \"-format\", specification.format!, \"-o\", artifactPath]\n    if (specification.format === \"UDZO\") {\n      args.push(\"-imagekey\", `zlib-level=${process.env.ELECTRON_BUILDER_COMPRESSION_LEVEL || \"9\"}`)\n    }\n    await spawn(\"hdiutil\", addLogLevel(args))\n    if (this.options.internetEnabled && parseInt(getOsRelease().split(\".\")[0], 10) < 19) {\n      await exec(\"hdiutil\", addLogLevel([\"internet-enable\"]).concat(artifactPath))\n    }\n\n    const licenseData = await addLicenseToDmg(packager, artifactPath)\n    if (packager.packagerOptions.effectiveOptionComputed != null) {\n      await packager.packagerOptions.effectiveOptionComputed({ licenseData })\n    }\n\n    if (this.options.sign === true) {\n      await this.signDmg(artifactPath)\n    }\n\n    const safeArtifactName = packager.computeSafeArtifactName(artifactName, \"dmg\")\n    const updateInfo = this.options.writeUpdateInfo === false ? null : await createBlockmap(artifactPath, this, packager, safeArtifactName)\n    await packager.info.callArtifactBuildCompleted({\n      file: artifactPath,\n      safeArtifactName,\n      target: this,\n      arch,\n      packager,\n      isWriteUpdateInfo: updateInfo != null,\n      updateInfo,\n    })\n  }\n\n  private async signDmg(artifactPath: string) {\n    if (!isSignAllowed(false)) {\n      return\n    }\n\n    const packager = this.packager\n    const qualifier = packager.platformSpecificBuildOptions.identity\n    // explicitly disabled if set to null\n    if (qualifier === null) {\n      // macPackager already somehow handle this situation, so, here just return\n      return\n    }\n\n    const keychainFile = (await packager.codeSigningInfo.value).keychainFile\n    const certificateType = \"Developer ID Application\"\n    let identity = await findIdentity(certificateType, qualifier, keychainFile)\n    if (identity == null) {\n      identity = await findIdentity(\"Mac Developer\", qualifier, keychainFile)\n      if (identity == null) {\n        return\n      }\n    }\n\n    const args = [\"--sign\", identity.hash!]\n    if (keychainFile != null) {\n      args.push(\"--keychain\", keychainFile)\n    }\n    args.push(artifactPath)\n    await exec(\"codesign\", args)\n  }\n\n  computeVolumeName(arch: Arch, custom?: string | null): string {\n    const appInfo = this.packager.appInfo\n    const shortVersion = this.packager.platformSpecificBuildOptions.bundleShortVersion || appInfo.version\n    const archString = getArchSuffix(arch, this.packager.platformSpecificBuildOptions.defaultArch)\n\n    if (custom == null) {\n      return `${appInfo.productFilename} ${shortVersion}${archString}`\n    }\n\n    return custom\n      .replace(/\\${arch}/g, archString)\n      .replace(/\\${shortVersion}/g, shortVersion)\n      .replace(/\\${version}/g, appInfo.version)\n      .replace(/\\${name}/g, appInfo.name)\n      .replace(/\\${productName}/g, appInfo.productName)\n  }\n\n  // public to test\n  async computeDmgOptions(): Promise<DmgOptions> {\n    const packager = this.packager\n    const specification: DmgOptions = { ...this.options }\n    if (specification.icon == null && specification.icon !== null) {\n      specification.icon = await packager.getIconPath()\n    }\n\n    if (specification.icon != null && isEmptyOrSpaces(specification.icon)) {\n      throw new InvalidConfigurationError(\"dmg.icon cannot be specified as empty string\")\n    }\n\n    const background = specification.background\n    if (specification.backgroundColor != null) {\n      if (background != null) {\n        throw new InvalidConfigurationError(\"Both dmg.backgroundColor and dmg.background are specified — please set the only one\")\n      }\n    } else if (background == null) {\n      specification.background = await computeBackground(packager)\n    } else {\n      specification.background = path.resolve(packager.info.projectDir, background)\n    }\n\n    if (specification.format == null) {\n      if (process.env.ELECTRON_BUILDER_COMPRESSION_LEVEL != null) {\n        ;(specification as any).format = \"UDZO\"\n      } else if (packager.compression === \"store\") {\n        specification.format = \"UDRO\"\n      } else {\n        specification.format = packager.compression === \"maximum\" ? \"UDBZ\" : \"UDZO\"\n      }\n    }\n\n    if (specification.contents == null) {\n      specification.contents = [\n        {\n          x: 130,\n          y: 220,\n        },\n        {\n          x: 410,\n          y: 220,\n          type: \"link\",\n          path: \"/Applications\",\n        },\n      ]\n    }\n    return specification\n  }\n}\n\nasync function createStageDmg(tempDmg: string, appPath: string, volumeName: string) {\n  //noinspection SpellCheckingInspection\n  const imageArgs = addLogLevel([\"create\", \"-srcfolder\", appPath, \"-volname\", volumeName, \"-anyowners\", \"-nospotlight\", \"-format\", \"UDRW\"])\n  if (log.isDebugEnabled) {\n    imageArgs.push(\"-debug\")\n  }\n\n  let filesystem = [\"HFS+\", \"-fsargs\", \"-c c=64,a=16,e=16\"]\n  if (process.arch === \"arm64\") {\n    // Apple Silicon `hdiutil` dropped support for HFS+, so we force the latest type\n    // https://github.com/electron-userland/electron-builder/issues/4606\n    filesystem = [\"APFS\"]\n    log.warn(null, \"Detected arm64 process, HFS+ is unavailable. Creating dmg with APFS - supports Mac OSX 10.12+\")\n  }\n  imageArgs.push(\"-fs\", ...filesystem)\n  imageArgs.push(tempDmg)\n  // The reason for retrying up to ten times is that hdiutil create in some cases fail to unmount due to \"resource busy\".\n  // https://github.com/electron-userland/electron-builder/issues/5431\n  await retry(() => spawn(\"hdiutil\", imageArgs), 5, 1000)\n  return tempDmg\n}\n\nfunction addLogLevel(args: Array<string>): Array<string> {\n  args.push(process.env.DEBUG_DMG === \"true\" ? \"-verbose\" : \"-quiet\")\n  return args\n}\n\nasync function computeAssetSize(cancellationToken: CancellationToken, dmgFile: string, specification: DmgOptions, backgroundFile: string | null | undefined) {\n  const asyncTaskManager = new AsyncTaskManager(cancellationToken)\n  asyncTaskManager.addTask(stat(dmgFile))\n\n  if (specification.icon != null) {\n    asyncTaskManager.addTask(statOrNull(specification.icon))\n  }\n\n  if (backgroundFile != null) {\n    asyncTaskManager.addTask(stat(backgroundFile))\n  }\n\n  let result = 32 * 1024\n  for (const stat of await asyncTaskManager.awaitTasks()) {\n    if (stat != null) {\n      result += stat.size\n    }\n  }\n  return result\n}\n\nasync function customizeDmg(volumePath: string, specification: DmgOptions, packager: MacPackager, backgroundFile: string | null | undefined) {\n  const window = specification.window\n  const isValidIconTextSize = !!specification.iconTextSize && specification.iconTextSize >= 10 && specification.iconTextSize <= 16\n  const iconTextSize = isValidIconTextSize ? specification.iconTextSize : 12\n  const env: any = {\n    ...process.env,\n    volumePath,\n    appFileName: `${packager.appInfo.productFilename}.app`,\n    iconSize: specification.iconSize || 80,\n    iconTextSize,\n\n    PYTHONIOENCODING: \"utf8\",\n  }\n\n  if (specification.backgroundColor != null || specification.background == null) {\n    env.backgroundColor = specification.backgroundColor || \"#ffffff\"\n\n    if (window != null) {\n      env.windowX = (window.x == null ? 100 : window.x).toString()\n      env.windowY = (window.y == null ? 400 : window.y).toString()\n      env.windowWidth = (window.width || 540).toString()\n      env.windowHeight = (window.height || 380).toString()\n    }\n  } else {\n    delete env.backgroundColor\n  }\n\n  const args = [\"dmg\", \"--volume\", volumePath]\n  if (specification.icon != null) {\n    args.push(\"--icon\", (await packager.getResource(specification.icon))!)\n  }\n  if (backgroundFile != null) {\n    args.push(\"--background\", backgroundFile)\n  }\n\n  const data: any = await executeAppBuilderAsJson(args)\n  if (data.backgroundWidth != null) {\n    env.windowWidth = window == null ? null : window.width\n    env.windowHeight = window == null ? null : window.height\n\n    if (env.windowWidth == null) {\n      env.windowWidth = data.backgroundWidth.toString()\n    }\n    if (env.windowHeight == null) {\n      env.windowHeight = data.backgroundHeight.toString()\n    }\n\n    if (env.windowX == null) {\n      env.windowX = 400\n    }\n    if (env.windowY == null) {\n      env.windowY = Math.round((1440 - env.windowHeight) / 2).toString()\n    }\n  }\n\n  Object.assign(env, data)\n\n  const asyncTaskManager = new AsyncTaskManager(packager.info.cancellationToken)\n  env.iconLocations = await computeDmgEntries(specification, volumePath, packager, asyncTaskManager)\n  await asyncTaskManager.awaitTasks()\n  const executePython = async (execName: string) => {\n    let pythonPath = process.env.PYTHON_PATH\n    if (!pythonPath) {\n      pythonPath = (await exec(\"which\", [execName])).trim()\n    }\n    await exec(pythonPath, [path.join(getDmgVendorPath(), \"dmgbuild/core.py\")], {\n      cwd: getDmgVendorPath(),\n      env,\n    })\n  }\n  try {\n    await executePython(\"python3\")\n  } catch (error: any) {\n    await executePython(\"python\")\n  }\n  return packager.packagerOptions.effectiveOptionComputed == null || !(await packager.packagerOptions.effectiveOptionComputed({ volumePath, specification, packager }))\n}\n\nasync function computeDmgEntries(specification: DmgOptions, volumePath: string, packager: MacPackager, asyncTaskManager: AsyncTaskManager): Promise<string> {\n  let result = \"\"\n  for (const c of specification.contents!) {\n    if (c.path != null && c.path.endsWith(\".app\") && c.type !== \"link\") {\n      log.warn({ path: c.path, reason: \"actual path to app will be used instead\" }, \"do not specify path for application\")\n    }\n\n    const entryPath = c.path || `${packager.appInfo.productFilename}.app`\n    const entryName = c.name || path.basename(entryPath)\n    const escapedEntryName = entryName.replace(/['\\\\]/g, match => `\\\\${match}`)\n    if (result.length !== 0) {\n      result += \",\\n\"\n    }\n    result += `'${escapedEntryName}': (${c.x}, ${c.y})`\n\n    if (c.type === \"link\") {\n      asyncTaskManager.addTask(exec(\"ln\", [\"-s\", `/${entryPath.startsWith(\"/\") ? entryPath.substring(1) : entryPath}`, `${volumePath}/${entryName}`]))\n    }\n    // use c.path instead of entryPath (to be sure that this logic is not applied to .app bundle) https://github.com/electron-userland/electron-builder/issues/2147\n    else if (!isEmptyOrSpaces(c.path) && (c.type === \"file\" || c.type === \"dir\")) {\n      const source = await packager.getResource(c.path)\n      if (source == null) {\n        log.warn({ entryPath, reason: \"doesn't exist\" }, \"skipped DMG item copying\")\n        continue\n      }\n\n      const destination = `${volumePath}/${entryName}`\n      asyncTaskManager.addTask(c.type === \"dir\" || (await stat(source)).isDirectory() ? copyDir(source, destination) : copyFile(source, destination))\n    }\n  }\n  return result\n}\n\nasync function transformBackgroundFileIfNeed(file: string, tmpDir: TmpDir): Promise<string> {\n  if (file.endsWith(\".tiff\") || file.endsWith(\".TIFF\")) {\n    return file\n  }\n\n  const retinaFile = file.replace(/\\.([a-z]+)$/, \"@2x.$1\")\n  if (await exists(retinaFile)) {\n    const tiffFile = await tmpDir.getTempFile({ suffix: \".tiff\" })\n    await exec(\"tiffutil\", [\"-cathidpicheck\", file, retinaFile, \"-out\", tiffFile])\n    return tiffFile\n  }\n\n  return file\n}\n"]}