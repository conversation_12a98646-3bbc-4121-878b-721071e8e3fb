{"name": "ssr-window", "version": "4.0.2", "description": "Better handling for window object in SSR environment", "main": "./ssr-window.esm.js", "module": "./ssr-window.esm.js", "exports": {"./package.json": "./package.json", ".": "./ssr-window.esm.js"}, "typings": "types/ssr-window.d.ts", "type": "module", "repository": {"type": "git", "url": "git+https://github.com/nolimits4web/ssr-window.git"}, "keywords": ["ssr", "window", "document"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/nolimits4web/ssr-window/issues"}, "homepage": "https://github.com/nolimits4web/ssr-window"}