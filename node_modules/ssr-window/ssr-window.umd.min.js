/**
 * SSR Window 4.0.2
 * Better handling for window object in SSR environment
 * https://github.com/nolimits4web/ssr-window
 *
 * Copyright 2021, <PERSON>
 *
 * Licensed under MIT
 *
 * Released on: December 13, 2021
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ssrWindow={})}(this,(function(e){"use strict";function t(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function n(e={},o={}){Object.keys(o).forEach((i=>{void 0===e[i]?e[i]=o[i]:t(o[i])&&t(e[i])&&Object.keys(o[i]).length>0&&n(e[i],o[i])}))}const o={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};const i={document:o,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};e.extend=n,e.getDocument=function(){const e="undefined"!=typeof document?document:{};return n(e,o),e},e.getWindow=function(){const e="undefined"!=typeof window?window:{};return n(e,i),e},e.ssrDocument=o,e.ssrWindow=i,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=ssr-window.umd.min.js.map