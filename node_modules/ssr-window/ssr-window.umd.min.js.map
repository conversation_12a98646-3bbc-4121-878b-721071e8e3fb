{"version": 3, "sources": ["../src/extend.ts", "../src/document.ts", "../src/window.ts"], "names": ["isObject", "obj", "constructor", "Object", "extend", "target", "src", "keys", "for<PERSON>ach", "key", "length", "ssrDocument", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "createElementNS", "importNode", "location", "hash", "host", "hostname", "href", "origin", "pathname", "protocol", "search", "ssrWindow", "document", "navigator", "userAgent", "history", "replaceState", "pushState", "go", "back", "CustomEvent", "this", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "matchMedia", "requestAnimationFrame", "callback", "cancelAnimationFrame", "id", "doc", "win", "window"], "mappings": ";;;;;;;;;;;iPACA,SAASA,EAASC,GAChB,OACU,OAARA,GACe,iBAARA,GACP,gBAAiBA,GACjBA,EAAIC,cAAgBC,OAIxB,SAASC,EAAOC,EAAc,GAAIC,EAAW,IAC3CH,OAAOI,KAAKD,GAAKE,SAASC,SACG,IAAhBJ,EAAOI,GAAsBJ,EAAOI,GAAOH,EAAIG,GAExDT,EAASM,EAAIG,KACbT,EAASK,EAAOI,KAChBN,OAAOI,KAAKD,EAAIG,IAAMC,OAAS,GAE/BN,EAAOC,EAAOI,GAAMH,EAAIG,aChBxBE,EAAc,CAClBC,KAAM,GACNC,qBACAC,wBACAC,cAAe,CACbC,SACAC,SAAU,IAEZC,cAAa,IACJ,KAETC,iBAAgB,IACP,GAETC,eAAc,IACL,KAETC,YAAW,KACF,CACLC,gBAGJC,cAAa,KACJ,CACLC,SAAU,GACVC,WAAY,GACZC,MAAO,GACPC,iBACAC,qBAAoB,IACX,KAIbC,gBAAe,KACN,IAETC,WAAU,IACD,KAETC,SAAU,CACRC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,WC9CNC,EAAY,CAChBC,SAAU9B,EACV+B,UAAW,CACTC,UAAW,IAEbZ,SAAU,CACRC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,IAEVK,QAAS,CACPC,iBACAC,cACAC,OACAC,UAEFC,YAAa,WACX,OAAOC,MAETrC,qBACAC,wBACAqC,iBAAgB,KACP,CACLC,iBAAgB,IACP,KAIbC,UACAC,SACAC,OAAQ,GACRC,eACAC,iBACAC,WAAU,KACD,IAETC,sBAAsBC,GACM,oBAAfJ,YACTI,IACO,MAEFJ,WAAWI,EAAU,GAE9BC,qBAAqBC,GACO,oBAAfN,YAGXC,aAAaK,8BDFjB,WACE,MAAMC,EACgB,oBAAbtB,SAA2BA,SAAY,GAEhD,OADArC,EAAO2D,EAAKpD,GACLoD,eCET,WACE,MAAMC,EAAgC,oBAAXC,OAAyBA,OAAU,GAE9D,OADA7D,EAAO4D,EAAKxB,GACLwB", "file": "ssr-window.umd.min.js"}