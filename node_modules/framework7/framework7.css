/**
 * Framework7 8.3.4
 * Full featured mobile HTML framework for building iOS & Android apps
 * https://framework7.io/
 *
 * Copyright 2014-2024 <PERSON>
 *
 * Released under the MIT License
 *
 * Released on: September 18, 2024
 */

/*====================
  Core
  ==================== */
:root {
  --f7-safe-area-left: 0px;
  --f7-safe-area-right: 0px;
  --f7-safe-area-top: 0px;
  --f7-safe-area-bottom: 0px;
  --f7-safe-area-outer-left: 0px;
  --f7-safe-area-outer-right: 0px;
  --f7-device-pixel-ratio: 1;
}
@supports (left: env(safe-area-inset-left)) {
  :root {
    --f7-safe-area-top: env(safe-area-inset-top);
    --f7-safe-area-bottom: env(safe-area-inset-bottom);
  }
  :root .ios-left-edge,
  :root .ios-edges,
  :root .safe-area-left,
  :root .safe-areas,
  :root .popup,
  :root .sheet-modal,
  :root .panel-left {
    --f7-safe-area-left: env(safe-area-inset-left);
    --f7-safe-area-outer-left: env(safe-area-inset-left);
  }
  :root .ios-right-edge,
  :root .ios-edges,
  :root .safe-area-right,
  :root .safe-areas,
  :root .popup,
  :root .sheet-modal,
  :root .panel-right {
    --f7-safe-area-right: env(safe-area-inset-right);
    --f7-safe-area-outer-right: env(safe-area-inset-right);
  }
  :root .no-safe-areas,
  :root .no-safe-area-left,
  :root .no-ios-edges,
  :root .no-ios-left-edge {
    --f7-safe-area-left: 0px;
    --f7-safe-area-outer-left: 0px;
  }
  :root .no-safe-areas,
  :root .no-safe-area-right,
  :root .no-ios-edges,
  :root .no-ios-right-edge {
    --f7-safe-area-right: 0px;
    --f7-safe-area-outer-right: 0px;
  }
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 2dppx) {
  :root {
    --f7-device-pixel-ratio: 2;
  }
}
@media (-webkit-min-device-pixel-ratio: 3), (min-resolution: 3dppx) {
  :root {
    --f7-device-pixel-ratio: 3;
  }
}
/*====================
  Fonts & Bars
  ==================== */
:root {
  --f7-font-size: 14px;
  --f7-bars-translucent-opacity: 0.8;
  --f7-bars-translucent-blur: 20px;
}
.ios {
  --f7-font-family: -apple-system, SF Pro Text, SF UI Text, system-ui, Helvetica Neue, Helvetica, Arial, sans-serif;
  --f7-line-height: 1.4;
  /*
  --f7-bars-link-color: var(--f7-theme-color);
  */
  --f7-bars-text-color: #000;
  --f7-text-color: #000;
  --f7-bars-bg-color: #f7f7f8;
  --f7-bars-bg-color-rgb: 247, 247, 248;
  --f7-bars-border-color: rgba(0, 0, 0, 0.25);
}
.ios .dark,
.ios.dark {
  --f7-bars-text-color: #fff;
  --f7-text-color: #fff;
  --f7-bars-bg-color: #121212;
  --f7-bars-bg-color-rgb: 22, 22, 22;
  --f7-bars-border-color: rgba(255, 255, 255, 0.16);
}
.md {
  --f7-font-family: Roboto, system-ui, Noto, Helvetica, Arial, sans-serif;
  --f7-line-height: 1.5;
  --f7-bars-border-color: transparent;
  --f7-text-color: #212121;
}
.md .dark,
.md.dark {
  --f7-text-color: rgba(255, 255, 255, 0.87);
}
.md,
.md .dark,
.md [class*='color-'] {
  --f7-bars-link-color: var(--f7-md-on-surface);
  --f7-bars-bg-color: var(--f7-md-surface-2);
  --f7-bars-bg-color-rgb: var(--f7-md-surface-2-rgb);
}
/*====================
  Color Themes
  ==================== */
.text-color-primary {
  --f7-theme-color-text-color: var(--f7-theme-color);
}
.bg-color-primary {
  --f7-theme-color-bg-color: var(--f7-theme-color);
}
.border-color-primary {
  --f7-theme-color-border-color: var(--f7-theme-color);
}
.ripple-color-primary {
  --f7-theme-color-ripple-color: rgba(var(--f7-theme-color-rgb), 0.3);
}
@font-face {
  font-family: 'framework7-core-icons';
  src: url('data:application/font-woff;charset=utf-8;base64, 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') format('woff');
  font-weight: 400;
  font-style: normal;
}
html {
  overscroll-behavior: none;
}
html,
body,
.framework7-root {
  position: relative;
  height: 100%;
  width: 100%;
  overflow-x: hidden;
}
body {
  margin: 0;
  padding: 0;
  width: 100%;
  background: #fff;
  overflow: hidden;
  overflow-y: scroll;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  font-family: var(--f7-font-family);
  font-size: var(--f7-font-size);
  line-height: var(--f7-line-height);
  color: var(--f7-text-color);
  scrollbar-width: none;
  scrollbar-color: transparent;
}
body::-webkit-scrollbar {
  -webkit-appearance: none;
          appearance: none;
  width: 0px;
  display: none;
  opacity: 0;
  background: transparent;
}
body::-webkit-scrollbar-track {
  box-shadow: none;
  background: transparent;
}
body::-webkit-scrollbar-thumb {
  background-color: transparent;
  outline: none;
  border-radius: 9999px;
  position: relative;
}
.dark body,
body.dark {
  background: #000;
}
.dark {
  color: var(--f7-text-color);
}
.framework7-root {
  overflow: hidden;
  box-sizing: border-box;
}
.framework7-initializing *,
.framework7-initializing *:before,
.framework7-initializing *:after {
  transition-duration: 0ms !important;
}
.device-ios,
.device-android {
  cursor: pointer;
}
.device-ios {
  touch-action: manipulation;
}
@media (width: 1024px) and (height: 691px) and (orientation: landscape) {
  html,
  body,
  .framework7-root {
    height: 671px;
  }
}
@media (width: 1024px) and (height: 692px) and (orientation: landscape) {
  html,
  body,
  .framework7-root {
    height: 672px;
  }
}
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-touch-callout: none;
}
a,
input,
textarea,
select {
  outline: 0;
}
a {
  cursor: pointer;
  text-decoration: none;
  color: var(--f7-theme-color);
}
.link,
.item-link {
  cursor: pointer;
}
p {
  margin: 1em 0;
}
.disabled {
  opacity: 0.55 !important;
  pointer-events: none !important;
}
html.device-full-viewport,
html.device-full-viewport body {
  height: 100vh;
}
.ios .md-only,
.ios .if-md,
.ios .if-not-ios,
.ios .not-ios {
  display: none !important;
}
@media (width: 1024px) and (height: 691px) and (orientation: landscape) {
  .ios,
  .ios body,
  .ios .framework7-root {
    height: 671px;
  }
}
@media (width: 1024px) and (height: 692px) and (orientation: landscape) {
  .ios,
  .ios body,
  .ios .framework7-root {
    height: 672px;
  }
}
.md .ios-only,
.md .if-ios,
.md .if-not-md,
.md .not-md {
  display: none !important;
}
/* === Statusbar === */
/* === Views === */
.views,
.view {
  position: relative;
  height: 100%;
  z-index: 5000;
  overflow: hidden;
  box-sizing: border-box;
}
.framework7-root > .view,
.framework7-root > .views {
  height: 100%;
}
.view-resize-handler {
  width: 6px;
  height: 100%;
  position: absolute;
  left: var(--f7-page-master-width);
  margin-left: -3px;
  top: 0;
  cursor: col-resize;
  z-index: 1000;
  display: none;
}
.view-master-detail .view-resize-handler {
  display: block;
}
/* === Pages === */
:root {
  --f7-page-master-width: 320px;
  --f7-page-master-border-color: rgba(0, 0, 0, 0.1);
  --f7-page-master-border-width: 1px;
  --f7-page-swipeback-transition-duration: 300ms;
  --f7-page-parallax-transition-duration: 500ms;
  --f7-page-cover-transition-duration: 450ms;
  --f7-page-dive-transition-duration: 500ms;
  --f7-page-fade-transition-duration: 500ms;
  --f7-page-flip-transition-duration: 700ms;
  --f7-page-push-transition-duration: 500ms;
  /*
  --f7-page-content-extra-padding-top: 0px;
  --f7-page-content-extra-padding-bottom: 0px;
  */
  --f7-page-title-line-height: 1.2;
  --f7-page-title-text-color: inherit;
  --f7-page-title-padding-left: 16px;
  --f7-page-title-padding-right: 16px;
}
.ios {
  --f7-page-transition-duration: 400ms;
  --f7-page-title-font-size: 34px;
  --f7-page-title-font-weight: 700;
  --f7-page-title-letter-spacing: -0.03em;
  --f7-page-title-padding-vertical: 6px;
  --f7-page-bg-color: #efeff4;
}
.ios .dark,
.ios.dark {
  --f7-page-bg-color: #000;
}
.md {
  --f7-page-transition-duration: 400ms;
  --f7-page-title-font-size: 34px;
  --f7-page-title-font-weight: 500;
  --f7-page-title-letter-spacing: 0;
  --f7-page-title-padding-vertical: 8px;
}
.md,
.md .dark,
.md [class*='color-'] {
  --f7-page-bg-color: var(--f7-md-surface);
}
.dark {
  --f7-page-master-border-color: rgba(255, 255, 255, 0.2);
}
.pages {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.page {
  box-sizing: border-box;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transform: none;
  background-color: var(--f7-page-bg-color);
  z-index: 1;
}
.page-with-navbar-large-collapsed {
  --f7-navbar-large-collapse-progress: 1;
}
.page-previous {
  pointer-events: none;
}
.page-content {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  box-sizing: border-box;
  height: 100%;
  position: relative;
  z-index: 1;
  padding-top: calc(var(--f7-page-navbar-offset, 0px) + var(--f7-page-toolbar-top-offset, 0px) + var(--f7-page-subnavbar-offset, 0px) + var(--f7-page-searchbar-offset, 0px) + var(--f7-page-content-extra-padding-top, 0px));
  padding-bottom: calc(var(--f7-page-toolbar-bottom-offset, 0px) + var(--f7-safe-area-bottom) + var(--f7-page-content-extra-padding-bottom, 0px));
}
.page-transitioning,
.page-transitioning .page-shadow-effect,
.page-transitioning .page-opacity-effect {
  transition-duration: var(--f7-page-transition-duration);
}
.page-transitioning-swipeback,
.page-transitioning-swipeback .page-shadow-effect,
.page-transitioning-swipeback .page-opacity-effect {
  transition-duration: var(--f7-page-swipeback-transition-duration);
}
.router-transition-forward .page-next,
.router-transition-backward .page-next,
.router-transition-forward .page-current,
.router-transition-backward .page-current {
  pointer-events: none;
}
.page-shadow-effect {
  position: absolute;
  top: 0;
  width: 16px;
  bottom: 0;
  z-index: -1;
  content: '';
  opacity: 0;
  right: 100%;
  background: linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.01) 50%, rgba(0, 0, 0, 0.2) 100%);
}
.page-opacity-effect {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.1);
  width: 100%;
  bottom: 0;
  content: '';
  opacity: 0;
  z-index: 10000;
}
.ios .page-previous {
  transform: translate3d(-20%, 0, 0);
}
.ios .page-next {
  transform: translate3d(100%, 0, 0);
}
.ios .page-previous .page-opacity-effect {
  opacity: 1;
}
.ios .page-previous:after {
  opacity: 1;
}
.ios .page-current .page-shadow-effect {
  opacity: 1;
}
.ios .router-transition-forward .page-next {
  animation: ios-page-next-to-current var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-forward .page-next:before {
  position: absolute;
  top: 0;
  width: 16px;
  bottom: 0;
  z-index: -1;
  content: '';
  opacity: 0;
  right: 100%;
  background: linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.01) 50%, rgba(0, 0, 0, 0.2) 100%);
  animation: ios-page-element-fade-in var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-forward .page-current {
  animation: ios-page-current-to-previous var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-forward .page-current:after {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.1);
  width: 100%;
  bottom: 0;
  content: '';
  opacity: 0;
  z-index: 10000;
  animation: ios-page-element-fade-in var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-backward .page-previous {
  animation: ios-page-previous-to-current var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-backward .page-previous:after {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.1);
  width: 100%;
  bottom: 0;
  content: '';
  opacity: 0;
  z-index: 10000;
  animation: ios-page-element-fade-out var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-backward .page-current {
  animation: ios-page-current-to-next var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-backward .page-current:before {
  position: absolute;
  top: 0;
  width: 16px;
  bottom: 0;
  z-index: -1;
  content: '';
  opacity: 0;
  right: 100%;
  background: linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.01) 50%, rgba(0, 0, 0, 0.2) 100%);
  animation: ios-page-element-fade-out var(--f7-page-transition-duration) forwards;
}
@keyframes ios-page-next-to-current {
  from {
    transform: translate3d(100%, 0, 0);
  }
  to {
    transform: translate3d(0%, 0, 0);
  }
}
@keyframes ios-page-previous-to-current {
  from {
    transform: translate3d(-20%, 0, 0);
  }
  to {
    transform: translate3d(0%, 0, 0);
  }
}
@keyframes ios-page-current-to-previous {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    transform: translate3d(-20%, 0, 0);
  }
}
@keyframes ios-page-current-to-next {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    transform: translate3d(100%, 0, 0);
  }
}
@keyframes ios-page-element-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes ios-page-element-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.md .page-previous {
  transform: translate3d(0, -20px, 0);
}
.md .page-next {
  pointer-events: none;
  transform: translate3d(0, 128px, 0);
  opacity: 0;
}
.md .page-next.page-next-on-right {
  transform: translate3d(100%, 0, 0);
  opacity: 1;
}
.md .router-transition-forward .page-next {
  opacity: 1;
  animation: md-page-next-to-current var(--f7-page-transition-duration) forwards cubic-bezier(0, 0.8, 0.3, 1);
}
.md .router-transition-forward .page-current {
  animation: md-page-current-to-previous var(--f7-page-transition-duration) forwards cubic-bezier(0, 0.8, 0.3, 1);
}
.md .router-transition-backward .page-current {
  animation: md-page-current-to-next calc(var(--f7-page-transition-duration) / 4) forwards cubic-bezier(0.8, 0, 1, 0.3), md-page-current-to-next-opacity 0ms calc(var(--f7-page-transition-duration) / 4) forwards;
}
.md .router-transition-backward .page-previous {
  animation: md-page-previous-to-current var(--f7-page-transition-duration) forwards;
}
@keyframes md-page-next-to-current {
  from {
    transform: translate3d(0, 128px, 0);
  }
  to {
    transform: translate3d(0, 0px, 0);
  }
}
@keyframes md-page-current-to-next {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    transform: translate3d(0, 128px, 0);
  }
}
@keyframes md-page-current-to-next-opacity {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes md-page-current-to-previous {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    transform: translate3d(0, -20px, 0);
  }
}
@keyframes md-page-previous-to-current {
  from {
    transform: translate3d(0, -20px, 0);
  }
  to {
    transform: translate3d(0, 0px, 0);
  }
}
.view:not(.view-master-detail) .page-master-stacked {
  display: none;
}
.view:not(.view-master-detail) .navbar-master-stacked {
  display: none;
}
.view-master-detail .page-master,
.view-master-detail .navbar-master {
  width: var(--f7-page-master-width);
  --f7-safe-area-right: 0px;
  --f7-safe-area-outer-right: 0px;
  border-right: var(--f7-page-master-border-width) solid var(--f7-page-master-border-color);
}
.view-master-detail .page-master-detail,
.view-master-detail .navbar-master-detail {
  width: calc(100% - var(--f7-page-master-width));
  --f7-safe-area-left: 0px;
  --f7-safe-area-outer-left: 0px;
  left: var(--f7-page-master-width);
}
.view-master-detail .page-master-detail > .navbar-master-detail {
  left: 0;
  right: 0;
  width: 100%;
}
.view-master-detail .page-master {
  z-index: 2;
  transform: none;
  pointer-events: auto;
}
.view-master-detail .page-master:before,
.view-master-detail .page-master:after {
  display: none;
}
.view-master-detail.router-transition .page-master {
  animation: none;
}
.md .router-transition-custom .page-previous,
.ios .router-transition-custom .page-previous,
.md .router-transition-custom .page-next,
.ios .router-transition-custom .page-next {
  opacity: 1;
  transform: none;
}
.md .router-transition-custom.view-master-detail .page-master,
.ios .router-transition-custom.view-master-detail .page-master {
  animation: none;
}
.page-title {
  position: relative;
  z-index: 10;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--f7-page-title-text-color);
  letter-spacing: var(--f7-page-title-letter-spacing);
  font-size: var(--f7-page-title-font-size);
  font-weight: var(--f7-page-title-font-weight);
  line-height: var(--f7-page-title-line-height);
  padding-left: calc(var(--f7-page-title-padding-left) + var(--f7-safe-area-left));
  padding-right: calc(var(--f7-page-title-padding-right) + var(--f7-safe-area-right));
  padding-top: var(--f7-page-title-padding-vertical);
  padding-bottom: var(--f7-page-title-padding-vertical);
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
}
.router-transition-f7-circle-forward:after,
.router-transition-f7-circle-backward:after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 100vmax;
  height: 100vmax;
  margin-left: -50vmax;
  margin-top: -50vmax;
  background: var(--f7-page-bg-color);
  z-index: 100;
}
.router-transition-f7-circle-forward .page,
.router-transition-f7-circle-backward .page {
  pointer-events: none;
}
.router-transition-f7-circle-forward:after {
  transform: scale(0);
  border-radius: 50%;
  animation: f7-circle-circle-in 400ms forwards;
}
.ios .router-transition-f7-circle-forward .page-next,
.md .router-transition-f7-circle-forward .page-next {
  opacity: 0;
  transform: scale(0.9);
  animation: f7-circle-next-to-current 300ms forwards;
  animation-delay: 300ms;
  z-index: 150;
}
.router-transition-f7-circle-backward:after {
  animation: f7-circle-circle-out 300ms forwards;
  animation-delay: 350ms;
}
.ios .router-transition-f7-circle-backward .page-current,
.md .router-transition-f7-circle-backward .page-current {
  animation: f7-circle-current-to-next 700ms forwards;
  z-index: 150;
}
@keyframes f7-circle-circle-in {
  from {
    transform: scale(0);
    border-radius: 50%;
  }
  50% {
    border-radius: 50%;
  }
  to {
    transform: scale(1);
    border-radius: 0%;
  }
}
@keyframes f7-circle-circle-out {
  from {
    transform: scale(1);
    border-radius: 0%;
  }
  50% {
    border-radius: 50%;
  }
  to {
    transform: scale(0);
    border-radius: 50%;
  }
}
@keyframes f7-circle-next-to-current {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  40% {
    transform: scale(1.035);
    opacity: 1;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
@keyframes f7-circle-current-to-next {
  from {
    transform: scale(1);
    opacity: 1;
  }
  34% {
    transform: scale(1.035);
    opacity: 1;
  }
  57% {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(0.9);
    opacity: 0;
  }
}
.router-transition-f7-cover-v-forward,
.router-transition-f7-cover-v-backward {
  background: #000;
  perspective: 1200px;
}
.router-transition-f7-cover-v-forward .page,
.router-transition-f7-cover-v-backward .page {
  pointer-events: none;
}
.router-transition-f7-cover-v-forward .page-next {
  animation: f7-cover-v-next-to-current var(--f7-page-cover-transition-duration) forwards;
}
.router-transition-f7-cover-v-forward .page-current {
  animation: f7-cover-v-current-to-prev var(--f7-page-cover-transition-duration) forwards;
}
.router-transition-f7-cover-v-backward .page-current {
  animation: f7-cover-v-current-to-next var(--f7-page-cover-transition-duration) forwards;
}
.router-transition-f7-cover-v-backward .page-previous {
  animation: f7-cover-v-prev-to-current var(--f7-page-cover-transition-duration) forwards;
}
@keyframes f7-cover-v-next-to-current {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0%);
  }
}
@keyframes f7-cover-v-current-to-next {
  from {
    transform: translateY(0%);
  }
  to {
    transform: translateY(100%);
  }
}
@keyframes f7-cover-v-current-to-prev {
  from {
    transform: translateZ(0);
    opacity: 1;
  }
  to {
    transform: translateZ(-300px);
    opacity: 0.5;
  }
}
@keyframes f7-cover-v-prev-to-current {
  from {
    transform: translateZ(-300px);
    opacity: 0.5;
  }
  to {
    transform: translateZ(0);
    opacity: 1;
  }
}
.router-transition-f7-cover-forward,
.router-transition-f7-cover-backward {
  background: #000;
  perspective: 1200px;
}
.router-transition-f7-cover-forward .page,
.router-transition-f7-cover-backward .page {
  pointer-events: none;
}
.router-transition-f7-cover-forward .page-next {
  animation: f7-cover-next-to-current var(--f7-page-cover-transition-duration) forwards;
}
.router-transition-f7-cover-forward .page-current {
  animation: f7-cover-current-to-prev var(--f7-page-cover-transition-duration) forwards;
}
.router-transition-f7-cover-backward .page-current {
  animation: f7-cover-current-to-next var(--f7-page-cover-transition-duration) forwards;
}
.router-transition-f7-cover-backward .page-previous {
  animation: f7-cover-prev-to-current var(--f7-page-cover-transition-duration) forwards;
}
@keyframes f7-cover-next-to-current {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0%);
  }
}
@keyframes f7-cover-current-to-next {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(100%);
  }
}
@keyframes f7-cover-current-to-prev {
  from {
    transform: translateZ(0);
    opacity: 1;
  }
  to {
    transform: translateZ(-300px);
    opacity: 0.5;
  }
}
@keyframes f7-cover-prev-to-current {
  from {
    transform: translateZ(-300px);
    opacity: 0.5;
  }
  to {
    transform: translateZ(0);
    opacity: 1;
  }
}
.router-transition-f7-dive-forward,
.router-transition-f7-dive-backward {
  background: var(--f7-page-bg-color);
  perspective: 1200px;
}
.router-transition-f7-dive-forward .page,
.router-transition-f7-dive-backward .page {
  pointer-events: none;
}
.router-transition-f7-dive-forward .page-next {
  animation: f7-dive-next-to-current var(--f7-page-dive-transition-duration) forwards;
}
.router-transition-f7-dive-forward .page-current {
  animation: f7-dive-current-to-prev var(--f7-page-dive-transition-duration) forwards;
}
.router-transition-f7-dive-backward .page-current {
  animation: f7-dive-current-to-next var(--f7-page-dive-transition-duration) forwards;
}
.router-transition-f7-dive-backward .page-previous {
  animation: f7-dive-prev-to-current var(--f7-page-dive-transition-duration) forwards;
}
@keyframes f7-dive-next-to-current {
  from {
    opacity: 0;
    transform: translateZ(-150px);
  }
  50% {
    opacity: 0;
  }
  to {
    opacity: 1;
    transform: translateZ(0px);
  }
}
@keyframes f7-dive-current-to-next {
  from {
    opacity: 1;
    transform: translateZ(0px);
  }
  50% {
    opacity: 0;
  }
  to {
    opacity: 0;
    transform: translateZ(-150px);
  }
}
@keyframes f7-dive-current-to-prev {
  from {
    transform: translateZ(0px);
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  to {
    opacity: 0;
    transform: translateZ(150px);
  }
}
@keyframes f7-dive-prev-to-current {
  from {
    opacity: 0;
    transform: translateZ(150px);
  }
  50% {
    opacity: 0;
  }
  to {
    opacity: 1;
    transform: translateZ(0px);
  }
}
.router-transition-f7-fade-forward,
.router-transition-f7-fade-backward {
  background: var(--f7-page-bg-color);
}
.router-transition-f7-fade-forward .page,
.router-transition-f7-fade-backward .page {
  pointer-events: none;
}
.ios .router-transition-f7-fade-forward .page-next,
.md .router-transition-f7-fade-forward .page-next {
  opacity: 0;
  animation: f7-fade-in var(--f7-page-fade-transition-duration) forwards;
}
.ios .router-transition-f7-fade-forward .page-current,
.md .router-transition-f7-fade-forward .page-current {
  animation: f7-fade-out var(--f7-page-fade-transition-duration) forwards;
}
.ios .router-transition-f7-fade-backward .page-current,
.md .router-transition-f7-fade-backward .page-current {
  animation: f7-fade-out var(--f7-page-fade-transition-duration) forwards;
}
.ios .router-transition-f7-fade-backward .page-previous,
.md .router-transition-f7-fade-backward .page-previous {
  animation: f7-fade-in var(--f7-page-fade-transition-duration) forwards;
}
@keyframes f7-fade-in {
  from {
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes f7-fade-out {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  to {
    opacity: 0;
  }
}
.router-transition-f7-flip-forward,
.router-transition-f7-flip-backward {
  background: #000;
  perspective: 1200px;
}
.router-transition-f7-flip-forward .page,
.router-transition-f7-flip-backward .page {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  overflow: hidden;
  pointer-events: none;
}
.router-transition-f7-flip-forward .page-next {
  transform-style: preserve-3d;
  animation: f7-flip-next-to-current var(--f7-page-flip-transition-duration) forwards;
}
.router-transition-f7-flip-forward .page-current {
  transform-style: preserve-3d;
  animation: f7-flip-current-to-prev var(--f7-page-flip-transition-duration) forwards;
}
.router-transition-f7-flip-backward .page-current {
  transform-style: preserve-3d;
  animation: f7-flip-current-to-next var(--f7-page-flip-transition-duration) forwards;
}
.router-transition-f7-flip-backward .page-previous {
  transform-style: preserve-3d;
  animation: f7-flip-prev-to-current var(--f7-page-flip-transition-duration) forwards;
}
@keyframes f7-flip-next-to-current {
  from {
    border-radius: 32px;
    transform: translateZ(-100vmax) rotateY(180deg);
  }
  to {
    border-radius: 0;
    transform: translateZ(0px) rotateY(0deg);
  }
}
@keyframes f7-flip-current-to-next {
  from {
    border-radius: 0px;
    transform: translateZ(0px) rotateY(0deg);
  }
  to {
    border-radius: 32px;
    transform: translateZ(-100vmax) rotateY(180deg);
  }
}
@keyframes f7-flip-current-to-prev {
  from {
    border-radius: 0px;
    transform: translateZ(0px) rotateY(0deg);
  }
  to {
    border-radius: 32px;
    transform: translateZ(-100vmax) rotateY(-180deg);
  }
}
@keyframes f7-flip-prev-to-current {
  from {
    border-radius: 32px;
    transform: translateZ(-100vmax) rotateY(-180deg);
  }
  to {
    border-radius: 0px;
    transform: translateZ(0px) rotateY(0deg);
  }
}
.router-transition-f7-parallax-forward .page {
  pointer-events: none;
}
.router-transition-f7-parallax-forward .page-next {
  animation: f7-parallax-next-to-current var(--f7-page-parallax-transition-duration) forwards;
}
.router-transition-f7-parallax-forward .page-current {
  animation: f7-parallax-current-to-prev var(--f7-page-parallax-transition-duration) forwards;
}
.router-transition-f7-parallax-backward .page {
  pointer-events: none;
}
.router-transition-f7-parallax-backward .page-current {
  animation: f7-parallax-current-to-next var(--f7-page-parallax-transition-duration) forwards;
}
.router-transition-f7-parallax-backward .page-previous {
  animation: f7-parallax-prev-to-current var(--f7-page-parallax-transition-duration) forwards;
}
@keyframes f7-parallax-next-to-current {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0%);
  }
}
@keyframes f7-parallax-current-to-next {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(100%);
  }
}
@keyframes f7-parallax-current-to-prev {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(-20%);
  }
}
@keyframes f7-parallax-prev-to-current {
  from {
    transform: translateX(-20%);
  }
  to {
    transform: translateX(0%);
  }
}
.router-transition-f7-push-forward .page {
  pointer-events: none;
}
.router-transition-f7-push-forward .page-next {
  animation: f7-push-next-to-current var(--f7-page-push-transition-duration) forwards;
}
.router-transition-f7-push-forward .page-current {
  animation: f7-push-current-to-prev var(--f7-page-push-transition-duration) forwards;
}
.router-transition-f7-push-backward .page {
  pointer-events: none;
}
.router-transition-f7-push-backward .page-current {
  animation: f7-push-current-to-next var(--f7-page-push-transition-duration) forwards;
}
.router-transition-f7-push-backward .page-previous {
  animation: f7-push-prev-to-current var(--f7-page-push-transition-duration) forwards;
}
@keyframes f7-push-next-to-current {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0%);
  }
}
@keyframes f7-push-current-to-next {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(100%);
  }
}
@keyframes f7-push-current-to-prev {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(-100%);
  }
}
@keyframes f7-push-prev-to-current {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0%);
  }
}
/* === Link === */
:root {
  --f7-link-highlight-black: rgba(0, 0, 0, 0.1);
  --f7-link-highlight-white: rgba(255, 255, 255, 0.15);
  --f7-link-highlight-color: rgba(var(--f7-theme-color-rgb), 0.15);
}
.ios {
  --f7-link-pressed-opacity: 0.3;
}
.link,
.tab-link {
  display: inline-flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  z-index: 1;
  --f7-touch-ripple-color: var(--f7-link-touch-ripple-color, rgba(var(--f7-theme-color-rgb), 0.25));
}
.link i + span,
.link i + i,
.link span + i,
.link span + span {
  margin-left: 4px;
}
.link[class*='color-'] {
  color: var(--f7-theme-color);
}
.ios .link {
  transition: opacity 300ms;
}
.ios .link.active-state {
  opacity: var(--f7-link-pressed-opacity);
  transition-duration: 0ms;
}
/* === Navbar === */
:root {
  /*
  --f7-navbar-bg-color: var(--f7-bars-bg-color);
  --f7-navbar-bg-color-rgb: var(--f7-bars-bg-color-rgb);
  --f7-navbar-border-color: var(--f7-bars-border-color);
  */
  --f7-navbar-hide-show-transition-duration: 400ms;
  --f7-navbar-title-line-height: 1.2;
  --f7-navbar-subtitle-text-align: inherit;
  --f7-navbar-large-title-line-height: 1.2;
  --f7-navbar-large-title-text-color: inherit;
  --f7-navbar-large-title-padding-left: 16px;
  --f7-navbar-large-title-padding-right: 16px;
}
.ios {
  /*
  --f7-navbar-link-color: var(--f7-bars-link-color);
  --f7-navbar-text-color: var(--f7-bars-text-color);
  */
  --f7-navbar-height: 44px;
  --f7-navbar-font-size: 17px;
  --f7-navbar-title-font-size: inherit;
  --f7-navbar-inner-padding-left: 8px;
  --f7-navbar-inner-padding-right: 8px;
  --f7-navbar-title-font-weight: 600;
  --f7-navbar-title-margin-left: 0;
  --f7-navbar-title-margin-right: 0;
  --f7-navbar-title-text-align: center;
  --f7-navbar-subtitle-font-size: 10px;
  --f7-navbar-subtitle-line-height: 1;
  --f7-navbar-large-title-height: 52px;
  --f7-navbar-large-title-font-size: 34px;
  --f7-navbar-large-title-font-weight: 700;
  --f7-navbar-large-title-letter-spacing: -0.03em;
  --f7-navbar-large-title-padding-vertical: 6px;
  /*
  --f7-navbar-link-height: var(--f7-navbar-height);
  --f7-navbar-link-line-height: var(--f7-navbar-height);
  */
  --f7-navbar-subtitle-text-color: rgba(0, 0, 0, 0.55);
}
.ios .dark,
.ios.dark {
  --f7-navbar-subtitle-text-color: rgba(255, 255, 255, 0.55);
}
.md {
  --f7-navbar-height: 64px;
  --f7-navbar-font-size: 16px;
  --f7-navbar-title-font-size: 22px;
  --f7-navbar-inner-padding-left: 0px;
  --f7-navbar-inner-padding-right: 0px;
  --f7-navbar-title-font-weight: 400;
  --f7-navbar-title-margin-left: 0px;
  --f7-navbar-title-margin-right: 16px;
  --f7-navbar-title-text-align: left;
  --f7-navbar-subtitle-font-size: 14px;
  --f7-navbar-subtitle-line-height: 1.2;
  --f7-navbar-large-title-font-size: 28px;
  --f7-navbar-large-title-height: 88px;
  --f7-navbar-large-title-font-weight: 400;
  --f7-navbar-large-title-letter-spacing: 0;
  --f7-navbar-large-title-padding-vertical: 8px;
  --f7-navbar-link-height: 48px;
  --f7-navbar-link-line-height: 48px;
}
.md,
.md .dark,
.md [class*='color-'] {
  --f7-navbar-link-color: var(--f7-md-on-surface);
  --f7-navbar-text-color: var(--f7-md-on-surface);
  --f7-navbar-subtitle-text-color: var(--f7-md-on-surface-variant);
}
.navbars,
.navbar {
  z-index: 500;
  left: 0;
  top: 0;
  width: 100%;
}
.navbars {
  position: absolute;
}
.navbars .navbar {
  z-index: auto;
}
.navbars .navbar-transitioning {
  z-index: 500;
}
.navbar {
  --f7-navbar-large-collapse-progress: 0;
  position: relative;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  box-sizing: border-box;
  margin: 0;
  height: calc(var(--f7-navbar-height) + var(--f7-safe-area-top));
  color: var(--f7-navbar-text-color, var(--f7-bars-text-color));
  font-size: var(--f7-navbar-font-size);
}
.navbar b {
  font-weight: 500;
}
.navbar a {
  color: var(--f7-navbar-link-color, var(--f7-bars-link-color, var(--f7-theme-color)));
}
.navbar a.link {
  display: flex;
  justify-content: flex-start;
  line-height: var(--f7-navbar-link-line-height, var(--f7-navbar-height));
  height: var(--f7-navbar-link-height, var(--f7-navbar-height));
}
.navbar .title,
.navbar .left,
.navbar .right {
  position: relative;
  z-index: 10;
}
.navbar .title {
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 10;
  font-weight: var(--f7-navbar-title-font-weight);
  display: inline-block;
  line-height: var(--f7-navbar-title-line-height);
  text-align: var(--f7-navbar-title-text-align);
  font-size: var(--f7-navbar-title-font-size);
  margin-left: var(--f7-navbar-title-margin-left);
  margin-right: var(--f7-navbar-title-margin-left);
}
.navbar .subtitle {
  display: block;
  color: var(--f7-navbar-subtitle-text-color);
  font-weight: normal;
  font-size: var(--f7-navbar-subtitle-font-size);
  line-height: var(--f7-navbar-subtitle-line-height);
  text-align: var(--f7-navbar-subtitle-text-align);
}
.navbar .left,
.navbar .right {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.navbar .right:first-child {
  position: absolute;
  height: 100%;
}
.navbar.no-outline .navbar-bg:after,
.navbar .no-outline .navbar-bg:after {
  display: none !important;
}
.navbar.no-outline .title-large:after,
.navbar .no-outline .title-large:after {
  display: none !important;
}
.navbar.navbar-hidden .navbar-bg:before,
.navbar-hidden .navbar .navbar-bg:before {
  opacity: 0 !important;
}
.navbar-bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  background: var(--f7-navbar-bg-color);
  background-color: var(--f7-navbar-bg-color, var(--f7-bars-bg-color));
  transition-property: transform;
}
@supports ((-webkit-backdrop-filter: blur(20px)) or (backdrop-filter: blur(20px))) {
  .ios-translucent-bars .navbar-bg {
    background-color: rgba(var(--f7-navbar-bg-color-rgb, var(--f7-bars-bg-color-rgb)), var(--f7-bars-translucent-opacity));
    -webkit-backdrop-filter: saturate(180%) blur(var(--f7-bars-translucent-blur));
            backdrop-filter: saturate(180%) blur(var(--f7-bars-translucent-blur));
  }
}
.navbar-bg:after,
.navbar-bg:before {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.navbar-bg:after {
  content: '';
  position: absolute;
  background-color: var(--f7-navbar-border-color, var(--f7-bars-border-color));
  display: block;
  z-index: 15;
  top: auto;
  right: auto;
  bottom: 0;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 100%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
.navbar-bg:after {
  z-index: 1;
}
.navbar-transitioning,
.navbar-transitioning .left,
.navbar-transitioning .title,
.navbar-transitioning .right,
.navbar-transitioning .title-large-text,
.navbar-transitioning .navbar-bg,
.navbar-transitioning .subnavbar,
.navbar-transitioning .navbar-bg:before {
  transition-duration: var(--f7-navbar-hide-show-transition-duration);
}
.navbar-page-transitioning {
  transition-duration: var(--f7-page-swipeback-transition-duration) !important;
}
.navbar-page-transitioning .title-large-text,
.navbar-page-transitioning .navbar-bg {
  transition-duration: var(--f7-page-swipeback-transition-duration) !important;
}
.navbar-hidden {
  transform: translate3d(0, calc(-1 * var(--f7-navbar-height)), 0);
}
.navbar-hidden .navbar-inner {
  pointer-events: none;
}
.navbar-hidden .navbar-inner > .left,
.navbar-hidden .navbar-inner > .title,
.navbar-hidden .navbar-inner > .right {
  opacity: 0 !important;
}
.navbar-hidden .subnavbar {
  pointer-events: auto;
}
.navbar-hidden-statusbar {
  transform: translate3d(0, calc(-1 * var(--f7-navbar-height) - var(--f7-safe-area-top)), 0);
}
.navbar-large-hidden .navbar-large {
  --f7-navbar-large-collapse-progress: 1;
}
.navbar-inner {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: var(--f7-safe-area-top) calc(var(--f7-navbar-inner-padding-right) + var(--f7-safe-area-right)) 0 calc(var(--f7-navbar-inner-padding-left) + var(--f7-safe-area-left));
  transform: translate3d(0, 0, 0);
  z-index: 10;
}
.views > .navbar,
.view > .navbar,
.page > .navbar,
.navbars > .navbar {
  position: absolute;
}
.navbar-transparent:not(.navbar-large):not(.navbar-transparent-visible):not(
    .with-searchbar-expandable-enabled
  ) .navbar-bg,
.navbar-transparent:not(.navbar-large):not(.navbar-transparent-visible):not(
    .with-searchbar-expandable-enabled
  ) .title {
  opacity: 0;
}
.navbar-large .navbar-bg {
  height: calc(100% + var(--f7-navbar-large-title-height));
  transform: translate3d(0px, calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)), 0);
}
.navbar-large-transparent .navbar-bg,
.navbar-large.navbar-transparent .navbar-bg {
  opacity: var(--f7-navbar-large-collapse-progress);
}
.ios .router-navbar-transition-to-large .navbar-bg,
.ios .router-navbar-transition-from-large .navbar-bg,
.navbar-bg.ios-swipeback-navbar-bg-large,
.navbar-large-transparent .navbar-bg.ios-swipeback-navbar-bg-large,
.navbar-large.navbar-transparent .navbar-bg.ios-swipeback-navbar-bg-large {
  height: calc(100% + var(--f7-navbar-large-title-height));
  transform: translate3d(0px, calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)), 0);
}
.ios .navbar:not(.navbar-large) .navbar-bg {
  --f7-navbar-large-collapse-progress: 1;
}
.navbar-large .title {
  opacity: var(--f7-navbar-large-collapse-progress);
}
.navbar-large-collapsed {
  --f7-navbar-large-collapse-progress: 1;
}
.navbar-large-collapsed .title-large {
  pointer-events: none;
}
.navbar .title-large {
  box-sizing: border-box;
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  display: flex;
  white-space: nowrap;
  overflow: hidden;
  height: var(--f7-navbar-large-title-height);
  z-index: 5;
}
.navbar .title-large-text {
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--f7-navbar-large-title-text-color);
  letter-spacing: var(--f7-navbar-large-title-letter-spacing);
  font-size: var(--f7-navbar-large-title-font-size);
  font-weight: var(--f7-navbar-large-title-font-weight);
  line-height: var(--f7-navbar-large-title-line-height);
  padding-left: calc(var(--f7-navbar-large-title-padding-left) + var(--f7-safe-area-left));
  padding-right: calc(var(--f7-navbar-large-title-padding-right) + var(--f7-safe-area-right));
  padding-top: var(--f7-navbar-large-title-padding-vertical);
  padding-bottom: var(--f7-navbar-large-title-padding-vertical);
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  transform: translate3d(0px, calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)), 0);
  transform-origin: calc(var(--f7-navbar-large-title-padding-left) + var(--f7-safe-area-left)) center;
}
.navbar-no-title-large-transition .title-large-text {
  transition-duration: 0ms;
}
.navbar ~ *,
.navbars ~ * {
  --f7-page-navbar-offset: calc(var(--f7-navbar-height) + var(--f7-safe-area-top));
}
.navbar ~ * .page-with-navbar-large,
.navbar ~ .page-with-navbar-large,
.navbars ~ * .page-with-navbar-large,
.navbars ~ .page-with-navbar-large,
.page-with-navbar-large .navbar ~ * {
  --f7-page-navbar-offset: calc(var(--f7-navbar-height) + var(--f7-navbar-large-title-height) + var(--f7-safe-area-top));
}
.page.no-navbar,
.page.no-navbar .navbar ~ * {
  --f7-page-navbar-offset: var(--f7-safe-area-top);
}
.ios {
  --f7-navbarLeftTextOffset: calc(4px + 12px + var(--f7-navbar-inner-padding-left));
  --f7-navbarTitleLargeOffset: var(--f7-navbar-large-title-padding-left);
  --f7-navbar-large-bg-center-top: translateX(0) translateY(calc(-1 * var(--f7-navbar-large-title-height)));
  --f7-navbar-large-bg-center-bottom: translateX(0) translateY(0);
  --f7-navbar-large-bg-left-top: translateX(-100%) translateY(calc(-1 * var(--f7-navbar-large-title-height)));
  --f7-navbar-large-bg-left-bottom: translateX(-100%) translateY(0);
  --f7-navbar-large-bg-right-top: translateX(100%) translateY(calc(-1 * var(--f7-navbar-large-title-height)));
  --f7-navbar-large-bg-right-bottom: translateX(100%) translateY(0);
}
.ios .navbar .title-large {
  align-items: flex-end;
}
.ios .navbar a.icon-only {
  width: 44px;
  margin: 0;
  justify-content: center;
}
.ios .navbar .left a + a,
.ios .navbar .right a + a {
  margin-left: 16px;
}
.ios .navbar b {
  font-weight: 600;
}
.ios .navbar .left {
  margin-right: 10px;
}
.ios .navbar .right {
  margin-left: 10px;
}
.ios .navbar .right:first-child {
  right: calc(8px + var(--f7-safe-area-right));
}
.ios .navbar-inner {
  justify-content: space-between;
}
.ios .navbar-inner-left-title {
  justify-content: flex-start;
}
.ios .navbar-inner-left-title .right {
  margin-left: auto;
}
.ios .navbar-inner-left-title .title {
  text-align: left;
  margin-right: 10px;
}
.ios .view-master-detail .navbar-previous:not(.navbar-master) .left,
.ios .view:not(.view-master-detail) .navbar-previous .left,
.ios .navbar-next .left,
.ios .view-master-detail .navbar-previous:not(.navbar-master) .title,
.ios .view:not(.view-master-detail) .navbar-previous .title,
.ios .navbar-next .title,
.ios .view-master-detail .navbar-previous:not(.navbar-master) .right,
.ios .view:not(.view-master-detail) .navbar-previous .right,
.ios .navbar-next .right,
.ios .view-master-detail .navbar-previous:not(.navbar-master) .subnavbar,
.ios .view:not(.view-master-detail) .navbar-previous .subnavbar,
.ios .navbar-next .subnavbar,
.ios .view-master-detail .navbar-previous:not(.navbar-master) .fading,
.ios .view:not(.view-master-detail) .navbar-previous .fading,
.ios .navbar-next .fading {
  opacity: 0;
}
.ios .view-master-detail .navbar-previous:not(.navbar-master),
.ios .view:not(.view-master-detail) .navbar-previous {
  pointer-events: none;
}
.ios .view-master-detail .navbar-previous:not(.navbar-master) .title-large,
.ios .view:not(.view-master-detail) .navbar-previous .title-large {
  opacity: 0;
  transition-duration: 0ms;
}
.ios .view-master-detail .navbar-previous:not(.navbar-master) .title-large .title-large-text,
.ios .view:not(.view-master-detail) .navbar-previous .title-large .title-large-text {
  transform: scale(0.5);
  transition-duration: 0ms;
}
.ios .view-master-detail .navbar-previous:not(.navbar-master) .subnavbar.sliding,
.ios .view:not(.view-master-detail) .navbar-previous .subnavbar.sliding,
.ios .view-master-detail .navbar-previous:not(.navbar-master) .sliding .subnavbar,
.ios .view:not(.view-master-detail) .navbar-previous .sliding .subnavbar {
  opacity: 1;
  transform: translate3d(-100%, 0, 0);
}
.ios .view:not(.view-master-detail) .navbar-previous .navbar-bg,
.ios .view-master-detail .navbar-previous:not(.navbar-master) .navbar-bg {
  transform: translateX(-100%);
}
.ios .navbar-next {
  pointer-events: none;
}
.ios .navbar-next .navbar-bg {
  transform: translateX(100%);
}
.ios .navbar-next .title-large .title-large-text {
  transition-duration: 0ms;
  transform: translateX(100%) translateY(calc(-1 * var(--f7-navbar-large-title-height)));
}
.ios .navbar-next .subnavbar.sliding,
.ios .navbar-next .sliding .subnavbar {
  opacity: 1;
  transform: translate3d(100%, 0, 0);
}
.ios .router-transition .navbar,
.ios .router-transition .navbar-bg {
  transition-duration: var(--f7-page-transition-duration);
}
.ios .router-transition .navbar-bg {
  animation-duration: var(--f7-page-transition-duration);
  animation-fill-mode: forwards;
}
.ios .router-transition .title-large,
.ios .router-transition .title-large-text {
  transition-duration: 0ms;
}
.ios .router-transition .navbar-current .left,
.ios .router-transition .navbar-current .title,
.ios .router-transition .navbar-current .right,
.ios .router-transition .navbar-current .subnavbar {
  animation: ios-navbar-element-fade-out var(--f7-page-transition-duration) forwards;
}
.ios .router-transition .navbar-current .sliding.left,
.ios .router-transition .navbar-current .sliding.left .icon + span,
.ios .router-transition .navbar-current .sliding.title,
.ios .router-transition .navbar-current .sliding.right,
.ios .router-transition .navbar-current .sliding .left,
.ios .router-transition .navbar-current .sliding .left .icon + span,
.ios .router-transition .navbar-current .sliding .title,
.ios .router-transition .navbar-current .sliding .right {
  transition-duration: var(--f7-page-transition-duration);
  opacity: 0 !important;
  animation: none;
}
.ios .router-transition .navbar-current .sliding .subnavbar,
.ios .router-transition .navbar-current .sliding.subnavbar {
  transition-duration: var(--f7-page-transition-duration);
  animation: none;
  opacity: 1;
}
.ios .router-transition-forward .navbar-next .left,
.ios .router-transition-backward .navbar-previous .left,
.ios .router-transition-forward .navbar-next .title,
.ios .router-transition-backward .navbar-previous .title,
.ios .router-transition-forward .navbar-next .right,
.ios .router-transition-backward .navbar-previous .right,
.ios .router-transition-forward .navbar-next .subnavbar,
.ios .router-transition-backward .navbar-previous .subnavbar {
  animation: ios-navbar-element-fade-in var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-forward .navbar-next .sliding.left,
.ios .router-transition-backward .navbar-previous .sliding.left,
.ios .router-transition-forward .navbar-next .sliding.left .icon + span,
.ios .router-transition-backward .navbar-previous .sliding.left .icon + span,
.ios .router-transition-forward .navbar-next .sliding.title,
.ios .router-transition-backward .navbar-previous .sliding.title,
.ios .router-transition-forward .navbar-next .sliding.right,
.ios .router-transition-backward .navbar-previous .sliding.right,
.ios .router-transition-forward .navbar-next .sliding .left,
.ios .router-transition-backward .navbar-previous .sliding .left,
.ios .router-transition-forward .navbar-next .sliding .left .icon + span,
.ios .router-transition-backward .navbar-previous .sliding .left .icon + span,
.ios .router-transition-forward .navbar-next .sliding .title,
.ios .router-transition-backward .navbar-previous .sliding .title,
.ios .router-transition-forward .navbar-next .sliding .right,
.ios .router-transition-backward .navbar-previous .sliding .right,
.ios .router-transition-forward .navbar-next .sliding .subnavbar,
.ios .router-transition-backward .navbar-previous .sliding .subnavbar {
  transition-duration: var(--f7-page-transition-duration);
  animation: none;
  transform: translate3d(0, 0, 0) !important;
  opacity: 1 !important;
}
.ios .router-transition-forward .navbar-next.navbar-transparent:not(.navbar-large):not(.navbar-transparent-visible) .title,
.ios .router-transition-backward .navbar-previous.navbar-transparent:not(.navbar-large):not(.navbar-transparent-visible) .title {
  animation: none;
}
.ios .router-transition-forward .navbar-next.navbar-transparent:not(.navbar-large):not(.navbar-transparent-visible) .sliding.title,
.ios .router-transition-backward .navbar-previous.navbar-transparent:not(.navbar-large):not(.navbar-transparent-visible) .sliding.title,
.ios .router-transition-forward .navbar-next.navbar-transparent:not(.navbar-large):not(.navbar-transparent-visible) .sliding .title,
.ios .router-transition-backward .navbar-previous.navbar-transparent:not(.navbar-large):not(.navbar-transparent-visible) .sliding .title {
  opacity: 0 !important;
}
.ios .router-transition-backward .navbar-previous.with-searchbar-expandable-enabled-no-transition .left,
.ios .router-transition-backward .navbar-previous.with-searchbar-expandable-enabled .left,
.ios .router-transition-backward .navbar-previous.with-searchbar-expandable-enabled-no-transition .title,
.ios .router-transition-backward .navbar-previous.with-searchbar-expandable-enabled .title,
.ios .router-transition-backward .navbar-previous.with-searchbar-expandable-enabled-no-transition .right,
.ios .router-transition-backward .navbar-previous.with-searchbar-expandable-enabled .right,
.ios .router-transition-backward .navbar-previous.with-searchbar-expandable-enabled-no-transition .subnavbar,
.ios .router-transition-backward .navbar-previous.with-searchbar-expandable-enabled .subnavbar {
  animation: none;
}
.ios .router-transition-forward .navbar-current.router-navbar-transition-from-large.router-navbar-transition-to-large .title-large,
.ios .router-transition-forward .navbar-current.router-navbar-transition-from-large:not(.router-navbar-transition-to-large) .title-large {
  overflow: visible;
}
.ios .router-transition-forward .navbar-current.router-navbar-transition-from-large.router-navbar-transition-to-large .title-large .title-large-text,
.ios .router-transition-forward .navbar-current.router-navbar-transition-from-large:not(.router-navbar-transition-to-large) .title-large .title-large-text {
  animation: ios-navbar-large-title-text-slide-up var(--f7-page-transition-duration) forwards, ios-navbar-large-title-text-fade-out var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-forward .navbar-next.router-navbar-transition-from-large .left .back span {
  animation: ios-navbar-back-text-next-to-current var(--f7-page-transition-duration) forwards;
  transition: none;
  transform-origin: left center;
}
.ios .router-transition-forward .navbar-next.router-navbar-transition-from-large.router-navbar-transition-to-large .title-large {
  overflow: visible;
}
.ios .router-transition-forward .navbar-next.router-navbar-transition-from-large.router-navbar-transition-to-large .title-large .title-large-text {
  animation: ios-navbar-large-title-text-slide-left var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-forward .navbar-next.router-navbar-transition-to-large:not(.router-navbar-transition-from-large) .title-large .title-large-text {
  animation: ios-navbar-large-title-text-slide-left var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-forward .navbar-next.navbar-large:not(.navbar-large-collapsed) .title,
.ios .router-transition-forward .navbar-current.navbar-large:not(.navbar-large-collapsed) .title {
  animation: none;
  opacity: 0 !important;
  transition-duration: 0;
}
.ios .router-transition-backward .navbar-current.router-navbar-transition-to-large .left .back span {
  animation: ios-navbar-back-text-current-to-previous var(--f7-page-transition-duration) forwards;
  transition: none;
  transform-origin: left center;
}
.ios .router-transition-backward .navbar-current.router-navbar-transition-from-large.router-navbar-transition-to-large .title-large {
  overflow: visible;
}
.ios .router-transition-backward .navbar-current.router-navbar-transition-from-large.router-navbar-transition-to-large .title-large .title-large-text {
  animation: ios-navbar-large-title-text-slide-right var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-backward .navbar-current.router-navbar-transition-from-large:not(.router-navbar-transition-to-large) .title-large .title-large-text {
  animation: ios-navbar-large-title-text-slide-right var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-backward .navbar-current.router-navbar-transition-to-large:not(.router-navbar-transition-from-large) .title-large {
  opacity: 0;
}
.ios .router-transition-backward .navbar-previous.router-navbar-transition-from-large.router-navbar-transition-to-large .title-large,
.ios .router-transition-backward .navbar-previous.router-navbar-transition-to-large:not(.router-navbar-transition-from-large) .title-large {
  overflow: visible;
  opacity: 1;
}
.ios .router-transition-backward .navbar-previous.router-navbar-transition-from-large.router-navbar-transition-to-large .title-large .title-large-text,
.ios .router-transition-backward .navbar-previous.router-navbar-transition-to-large:not(.router-navbar-transition-from-large) .title-large .title-large-text {
  animation: ios-navbar-large-title-text-slide-down var(--f7-page-transition-duration) forwards, ios-navbar-large-title-text-fade-in var(--f7-page-transition-duration) forwards;
}
.ios .router-transition-backward .navbar-current.navbar-large:not(.navbar-large-collapsed) .title,
.ios .router-transition-backward .navbar-previous.navbar-large:not(.navbar-large-collapsed) .title {
  animation: none;
  opacity: 0 !important;
  transition-duration: 0;
}
.ios .router-transition-forward .navbar-current .navbar-bg {
  animation-name: ios-navbar-bg-from-cb-to-lb;
}
.ios .router-transition-forward .navbar-current.router-navbar-transition-from-large.router-navbar-transition-to-large .navbar-bg {
  animation-name: ios-navbar-bg-from-cb-to-lb;
}
.ios .router-transition-forward .navbar-current.router-navbar-transition-from-large:not(.router-navbar-transition-to-large) .navbar-bg {
  animation-name: ios-navbar-bg-from-cb-to-lt;
}
.ios .router-transition-forward .navbar-current:not(.router-navbar-transition-from-large).router-navbar-transition-to-large .navbar-bg {
  animation-name: ios-navbar-bg-from-ct-to-lb;
}
.ios .router-transition-forward .navbar-current.navbar-large-collapsed:not(.router-navbar-transition-to-large) .navbar-bg {
  animation-name: ios-navbar-bg-from-ct-to-lt;
}
.ios .router-transition-forward .navbar-next .navbar-bg {
  animation-name: ios-navbar-bg-from-rb-to-cb;
}
.ios .router-transition-forward .navbar-next.router-navbar-transition-from-large.router-navbar-transition-to-large .navbar-bg {
  animation-name: ios-navbar-bg-from-rb-to-cb;
}
.ios .router-transition-forward .navbar-next.router-navbar-transition-from-large:not(.router-navbar-transition-to-large) .navbar-bg {
  animation-name: ios-navbar-bg-from-rb-to-ct;
}
.ios .router-transition-forward .navbar-next:not(.router-navbar-transition-from-large).router-navbar-transition-to-large .navbar-bg {
  animation-name: ios-navbar-bg-from-rt-to-cb;
}
.ios .router-transition-backward .navbar-current .navbar-bg {
  animation-name: ios-navbar-bg-from-cb-to-rb;
}
.ios .router-transition-backward .navbar-current:not(.router-navbar-transition-from-large).router-navbar-transition-to-large .navbar-bg {
  animation-name: ios-navbar-bg-from-ct-to-rb;
}
.ios .router-transition-backward .navbar-current.router-navbar-transition-from-large:not(.router-navbar-transition-to-large) .navbar-bg {
  animation-name: ios-navbar-bg-from-cb-to-rt;
}
.ios .router-transition-backward .navbar-current.navbar-large-collapsed .navbar-bg {
  animation-name: ios-navbar-bg-from-ct-to-rt;
}
.ios .router-transition-backward .navbar-current.navbar-large-collapsed.router-navbar-transition-to-large .navbar-bg {
  animation-name: ios-navbar-bg-from-ct-to-rb;
}
.ios .router-transition-backward .navbar-previous .navbar-bg {
  animation-name: ios-navbar-bg-from-lb-to-cb;
}
.ios .router-transition-backward .navbar-previous:not(.router-navbar-transition-from-large).router-navbar-transition-to-large .navbar-bg {
  animation-name: ios-navbar-bg-from-lt-to-cb;
}
.ios .router-transition-backward .navbar-previous.router-navbar-transition-from-large:not(.router-navbar-transition-to-large) .navbar-bg {
  animation-name: ios-navbar-bg-from-lb-to-ct;
}
.ios .router-transition-backward .navbar-previous.navbar-large-collapsed .navbar-bg {
  animation-name: ios-navbar-bg-from-lt-to-ct;
}
.view-master-detail .navbars {
  z-index: auto;
}
.view-master-detail .page-master {
  z-index: 525;
}
.view-master-detail .navbar-master .navbar-inner,
.view-master-detail .navbar-master .navbar-bg {
  z-index: 550;
}
.view-master-detail .navbar-master-detail .navbar-inner,
.view-master-detail .navbar-master-detail .navbar-bg {
  z-index: 500;
}
.view-master-detail .navbar-master.navbar-previous {
  pointer-events: auto;
}
.view-master-detail .navbar-master.navbar-previous .left,
.view-master-detail .navbar-master.navbar-previous:not(.navbar-large) .title,
.view-master-detail .navbar-master.navbar-previous .right,
.view-master-detail .navbar-master.navbar-previous .subnavbar {
  opacity: 1;
}
.view-master-detail.router-transition .navbar-master .left,
.view-master-detail.router-transition .navbar-master .left .icon + span,
.view-master-detail.router-transition .navbar-master:not(.navbar-large) .title,
.view-master-detail.router-transition .navbar-master .right,
.view-master-detail.router-transition .navbar-master .subnavbar,
.view-master-detail.router-transition .navbar-master .fading {
  opacity: 1 !important;
  transition-duration: 0ms;
  transform: none !important;
  animation: none !important;
}
.view-master-detail.router-transition .navbar-master .navbar-bg {
  transition-duration: 0ms;
  animation: none !important;
}
.view-master-detail.router-transition .navbar-master.navbar-large .title {
  opacity: calc(-1 + 2 * var(--f7-navbar-large-collapse-progress)) !important;
  transition-duration: 0ms;
  transform: none !important;
  animation: none !important;
}
.view-master-detail.router-transition .navbar-master.navbar-large .title-large,
.view-master-detail.router-transition .navbar-master.navbar-large .title-large-text {
  transition-duration: 0ms;
  animation: none !important;
}
.view-master-detail.router-transition .navbar-master.navbar-large-transparent .navbar-bg,
.view-master-detail.router-transition .navbar-master.navbar-large.navbar-transparent .navbar-bg {
  height: 100% !important;
  opacity: var(--f7-navbar-large-collapse-progress) !important;
}
@keyframes ios-navbar-element-fade-in {
  0% {
    opacity: 0;
  }
  25% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes ios-navbar-element-fade-out {
  from {
    opacity: 1;
  }
  75% {
    opacity: 0;
  }
  to {
    opacity: 0;
  }
}
@keyframes ios-navbar-large-title-text-slide-up {
  0% {
    transform: translateX(0px) translateY(0%) scale(1);
  }
  100% {
    transform: translateX(calc(var(--f7-navbarLeftTextOffset) - var(--f7-navbarTitleLargeOffset))) translateY(calc(-1 * var(--f7-navbar-large-title-height) + var(--f7-navbar-large-title-padding-vertical))) scale(0.5);
  }
}
@keyframes ios-navbar-large-title-text-slide-down {
  0% {
    transform: translateX(calc(var(--f7-navbarLeftTextOffset) - var(--f7-navbarTitleLargeOffset))) translateY(calc(-1 * var(--f7-navbar-large-title-height) + var(--f7-navbar-large-title-padding-vertical) / 2)) scale(0.5);
  }
  100% {
    transform: translateX(0px) translateY(0%) scale(1);
  }
}
@keyframes ios-navbar-large-title-text-slide-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0%);
  }
}
@keyframes ios-navbar-large-title-text-slide-right {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}
@keyframes ios-navbar-large-title-text-fade-out {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}
@keyframes ios-navbar-large-title-text-fade-in {
  0% {
    opacity: 0;
  }
  20% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes ios-navbar-back-text-current-to-previous {
  0% {
    opacity: 1;
    transform: translateY(0px) translateX(0px) scale(1);
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
    transform: translateX(calc(var(--f7-navbarTitleLargeOffset) - var(--f7-navbarLeftTextOffset))) translateY(calc(1 * var(--f7-navbar-large-title-height) - var(--f7-navbar-large-title-padding-vertical) / 2)) scale(2);
  }
}
@keyframes ios-navbar-back-text-next-to-current {
  0% {
    opacity: 0;
    transform: translateX(calc(var(--f7-navbarTitleLargeOffset) - var(--f7-navbarLeftTextOffset))) translateY(calc(1 * var(--f7-navbar-large-title-height) + var(--f7-navbar-large-title-padding-vertical) / 2)) scale(2);
  }
  20% {
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: translateX(0px) translateY(0px) scale(1);
  }
}
@keyframes ios-navbar-bg-from-cb-to-lb {
  from {
    transform: var(--f7-navbar-large-bg-center-bottom);
  }
  to {
    transform: var(--f7-navbar-large-bg-left-bottom);
  }
}
@keyframes ios-navbar-bg-from-cb-to-lt {
  from {
    transform: var(--f7-navbar-large-bg-center-bottom);
  }
  to {
    transform: var(--f7-navbar-large-bg-left-top);
  }
}
@keyframes ios-navbar-bg-from-ct-to-lb {
  from {
    transform: var(--f7-navbar-large-bg-center-top);
  }
  to {
    transform: var(--f7-navbar-large-bg-left-bottom);
  }
}
@keyframes ios-navbar-bg-from-ct-to-lt {
  from {
    transform: var(--f7-navbar-large-bg-center-top);
  }
  to {
    transform: var(--f7-navbar-large-bg-left-top);
  }
}
@keyframes ios-navbar-bg-from-rb-to-cb {
  from {
    transform: var(--f7-navbar-large-bg-right-bottom);
  }
  to {
    transform: var(--f7-navbar-large-bg-center-bottom);
  }
}
@keyframes ios-navbar-bg-from-rb-to-ct {
  from {
    transform: var(--f7-navbar-large-bg-right-bottom);
  }
  to {
    transform: var(--f7-navbar-large-bg-center-top);
  }
}
@keyframes ios-navbar-bg-from-rt-to-cb {
  from {
    transform: var(--f7-navbar-large-bg-right-top);
  }
  to {
    transform: var(--f7-navbar-large-bg-center-bottom);
  }
}
@keyframes ios-navbar-bg-from-cb-to-rb {
  from {
    transform: var(--f7-navbar-large-bg-center-bottom);
  }
  to {
    transform: var(--f7-navbar-large-bg-right-bottom);
  }
}
@keyframes ios-navbar-bg-from-ct-to-rb {
  from {
    transform: var(--f7-navbar-large-bg-center-top);
  }
  to {
    transform: var(--f7-navbar-large-bg-right-bottom);
  }
}
@keyframes ios-navbar-bg-from-cb-to-rt {
  from {
    transform: var(--f7-navbar-large-bg-center-bottom);
  }
  to {
    transform: var(--f7-navbar-large-bg-right-top);
  }
}
@keyframes ios-navbar-bg-from-ct-to-rt {
  from {
    transform: var(--f7-navbar-large-bg-center-top);
  }
  to {
    transform: var(--f7-navbar-large-bg-right-top);
  }
}
@keyframes ios-navbar-bg-from-lb-to-cb {
  from {
    transform: var(--f7-navbar-large-bg-left-bottom);
  }
  to {
    transform: var(--f7-navbar-large-bg-center-bottom);
  }
}
@keyframes ios-navbar-bg-from-lt-to-cb {
  from {
    transform: var(--f7-navbar-large-bg-left-top);
  }
  to {
    transform: var(--f7-navbar-large-bg-center-bottom);
  }
}
@keyframes ios-navbar-bg-from-lb-to-ct {
  from {
    transform: var(--f7-navbar-large-bg-left-bottom);
  }
  to {
    transform: var(--f7-navbar-large-bg-center-top);
  }
}
@keyframes ios-navbar-bg-from-lt-to-ct {
  from {
    transform: var(--f7-navbar-large-bg-left-top);
  }
  to {
    transform: var(--f7-navbar-large-bg-center-top);
  }
}
.md .navbar .title-large {
  align-items: center;
}
.md .navbar .left {
  margin-left: 4px;
  margin-right: 4px;
}
.md .navbar a.link {
  padding: 0 12px;
  min-width: 48px;
}
.md .navbar a.icon-only {
  min-width: 0;
  flex-shrink: 0;
  width: 48px;
}
.md .navbar .right {
  margin-left: auto;
  margin-right: 4px;
}
.md .navbar .right:first-child {
  right: var(--f7-safe-area-right);
}
.md .navbar-inner {
  justify-content: flex-start;
  overflow: hidden;
}
.md .navbar-large:not(.navbar-large-collapsed) .navbar-inner {
  overflow: visible;
}
.md .page.page-with-subnavbar .navbar-inner {
  overflow: visible;
}
.md .navbar-inner:not(.navbar-inner-centered-title) .title:first-child {
  margin-left: 16px;
}
.md .navbar-inner-centered-title {
  justify-content: space-between;
}
.md .navbar-inner-centered-title .right {
  margin-left: 0;
}
.md .navbar-inner-centered-title .title {
  text-align: center;
}
/* === Toolbar === */
:root {
  /*
  --f7-toolbar-bg-color: var(--f7-bars-bg-color);
  --f7-toolbar-bg-color-rgb: var(--f7-bars-bg-color-rgb);
  --f7-toolbar-border-color: var(--f7-bars-border-color);
  --f7-toolbar-link-color: var(--f7-bars-link-color);
  --f7-toolbar-text-color: var(--f7-bars-text-color);
  */
  --f7-tabbar-link-active-bg-color: transparent;
  --f7-tabbar-label-text-transform: none;
  --f7-toolbar-hide-show-transition-duration: 400ms;
}
.ios {
  --f7-toolbar-height: 44px;
  --f7-toolbar-font-size: 17px;
  --f7-toolbar-inner-padding-left: 8px;
  --f7-toolbar-inner-padding-right: 8px;
  --f7-toolbar-link-font-weight: 400;
  /*
  --f7-toolbar-link-height: var(--f7-toolbar-height);
  --f7-toolbar-link-line-height: var(--f7-toolbar-height);
  --f7-tabbar-link-active-color: var(--f7-theme-color);
  */
  --f7-tabbar-icons-height: 50px;
  --f7-tabbar-icons-tablet-height: 50px;
  --f7-tabbar-icon-size: 28px;
  --f7-tabbar-link-text-transform: none;
  --f7-tabbar-link-font-weight: 400;
  --f7-tabbar-link-letter-spacing: 0;
  --f7-tabbar-label-font-size: 12px;
  --f7-tabbar-label-tablet-font-size: 14px;
  --f7-tabbar-label-font-weight: 500;
  --f7-tabbar-label-letter-spacing: 0.01;
  --f7-tabbar-link-inactive-color: rgba(0, 0, 0, 0.4);
}
.ios .dark,
.ios.dark {
  --f7-tabbar-link-inactive-color: rgba(255, 255, 255, 0.54);
}
.md {
  --f7-toolbar-height: 56px;
  --f7-toolbar-font-size: 14px;
  --f7-toolbar-inner-padding-left: 0px;
  --f7-toolbar-inner-padding-right: 0px;
  /*
  --f7-toolbar-link-height: var(--f7-toolbar-height);
  --f7-toolbar-link-line-height: var(--f7-toolbar-height);
  */
  --f7-tabbar-icons-height: 80px;
  --f7-tabbar-icons-tablet-height: 80px;
  --f7-tabbar-icon-size: 24px;
  --f7-tabbar-link-text-transform: none;
  --f7-tabbar-link-font-weight: 500;
  --f7-tabbar-link-letter-spacing: 0;
  --f7-toolbar-link-font-weight: 500;
  --f7-tabbar-label-font-size: 12px;
  --f7-tabbar-label-tablet-font-size: 12px;
  --f7-tabbar-label-font-weight: 500;
  --f7-tabbar-label-letter-spacing: 0;
}
.md,
.md .dark,
.md [class*='color-'] {
  --f7-tabbar-link-active-color: var(--f7-md-on-secondary-container);
  --f7-tabbar-link-active-border-color: var(--f7-md-primary);
  --f7-tabbar-link-inactive-color: var(--f7-md-on-surface-variant);
  --f7-tabbar-link-active-icon-bg-color: var(--f7-md-secondary-container);
}
.toolbar {
  width: 100%;
  position: relative;
  margin: 0;
  transform: translate3d(0, 0, 0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  z-index: 600;
  box-sizing: border-box;
  left: 0;
  height: var(--f7-toolbar-height);
  background-color: var(--f7-toolbar-bg-color, var(--f7-bars-bg-color));
  color: var(--f7-toolbar-text-color, var(--f7-bars-text-color));
  font-size: var(--f7-toolbar-font-size);
}
@supports ((-webkit-backdrop-filter: blur(20px)) or (backdrop-filter: blur(20px))) {
  .ios-translucent-bars .toolbar {
    background-color: rgba(var(--f7-toolbar-bg-color-rgb, var(--f7-bars-bg-color-rgb)), var(--f7-bars-translucent-opacity));
    -webkit-backdrop-filter: saturate(180%) blur(var(--f7-bars-translucent-blur));
            backdrop-filter: saturate(180%) blur(var(--f7-bars-translucent-blur));
  }
}
.toolbar b {
  font-weight: 600;
}
.toolbar a {
  color: var(--f7-toolbar-link-color, var(--f7-bars-link-color, var(--f7-theme-color)));
  box-sizing: border-box;
  flex-shrink: 1;
  position: relative;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.toolbar .link {
  display: flex;
  line-height: var(--f7-toolbar-link-line-height, var(--f7-toolbar-height));
  height: var(--f7-toolbar-link-height, var(--f7-toolbar-height));
  font-weight: var(--f7-toolbar-link-font-weight);
}
.toolbar i.icon {
  display: block;
}
.toolbar:after,
.toolbar:before {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.views > .toolbar,
.view > .toolbar,
.page > .toolbar {
  position: absolute;
}
.toolbar-top,
.ios .toolbar-top-ios,
.md .toolbar-top-md {
  top: 0;
}
.toolbar-top .tab-link-highlight,
.ios .toolbar-top-ios .tab-link-highlight,
.md .toolbar-top-md .tab-link-highlight {
  bottom: 0;
}
.toolbar-top.no-outline:after,
.ios .toolbar-top-ios.no-outline:after,
.md .toolbar-top-md.no-outline:after {
  display: none !important;
}
.toolbar-top.toolbar-hidden:before,
.ios .toolbar-top-ios.toolbar-hidden:before,
.md .toolbar-top-md.toolbar-hidden:before {
  display: none !important;
}
.toolbar-top:after,
.ios .toolbar-top-ios:after,
.md .toolbar-top-md:after,
.toolbar-top:before,
.ios .toolbar-top-ios:before,
.md .toolbar-top-md:before {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.toolbar-top:after,
.ios .toolbar-top-ios:after,
.md .toolbar-top-md:after {
  content: '';
  position: absolute;
  background-color: var(--f7-toolbar-border-color, var(--f7-bars-border-color));
  display: block;
  z-index: 15;
  top: auto;
  right: auto;
  bottom: 0;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 100%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
.toolbar-bottom,
.ios .toolbar-bottom-ios,
.md .toolbar-bottom-md {
  bottom: 0;
  height: calc(var(--f7-toolbar-height) + var(--f7-safe-area-bottom));
}
.toolbar-bottom .tab-link-highlight,
.ios .toolbar-bottom-ios .tab-link-highlight,
.md .toolbar-bottom-md .tab-link-highlight {
  top: 0;
}
.toolbar-bottom .toolbar-inner,
.ios .toolbar-bottom-ios .toolbar-inner,
.md .toolbar-bottom-md .toolbar-inner {
  height: auto;
  top: 0;
  bottom: var(--f7-safe-area-bottom);
}
.toolbar-bottom.no-outline:before,
.ios .toolbar-bottom-ios.no-outline:before,
.md .toolbar-bottom-md.no-outline:before {
  display: none !important;
}
.toolbar-bottom.toolbar-hidden:after,
.ios .toolbar-bottom-ios.toolbar-hidden:after,
.md .toolbar-bottom-md.toolbar-hidden:after {
  display: none !important;
}
.toolbar-bottom:before,
.ios .toolbar-bottom-ios:before,
.md .toolbar-bottom-md:before {
  content: '';
  position: absolute;
  background-color: var(--f7-toolbar-border-color, var(--f7-bars-border-color));
  display: block;
  z-index: 15;
  top: 0;
  right: auto;
  bottom: auto;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 0%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
.toolbar-inner {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
  align-content: center;
  overflow: hidden;
  padding: 0 calc(var(--f7-toolbar-inner-padding-right) + var(--f7-safe-area-right)) 0 calc(var(--f7-toolbar-inner-padding-left) + var(--f7-safe-area-left));
}
.views > .tabbar,
.views > .tabbar-icons {
  z-index: 5001;
}
.tabbar a,
.tabbar-icons a {
  color: var(--f7-tabbar-link-inactive-color);
}
.tabbar .link,
.tabbar-icons .link {
  line-height: 1.4;
}
.tabbar .tab-link,
.tabbar-icons .tab-link,
.tabbar .link,
.tabbar-icons .link {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-transform: var(--f7-tabbar-link-text-transform);
  font-weight: var(--f7-tabbar-link-font-weight);
  letter-spacing: var(--f7-tabbar-link-letter-spacing);
  overflow: hidden;
}
.tabbar .tab-link-active,
.tabbar-icons .tab-link-active {
  color: var(--f7-tabbar-link-active-color, var(--f7-theme-color));
  background-color: var(--f7-tabbar-link-active-bg-color, transparent);
}
.tabbar i.icon,
.tabbar-icons i.icon {
  font-size: var(--f7-tabbar-icon-size);
  height: var(--f7-tabbar-icon-size);
  line-height: var(--f7-tabbar-icon-size);
}
.md .tabbar.tabbar-icons .tab-link-highlight,
.md .tabbar-icons.tabbar-icons .tab-link-highlight {
  display: none;
}
.md .tabbar:not(.tabbar-icons) .tab-link-highlight,
.md .tabbar-icons:not(.tabbar-icons) .tab-link-highlight {
  position: absolute;
  height: 2px;
  background: var(--f7-tabbar-link-active-border-color, var(--f7-theme-color));
  transition-duration: 300ms;
  left: 0;
}
.tabbar-icons {
  --f7-toolbar-height: var(--f7-tabbar-icons-height);
}
.tabbar-icons .tab-link,
.tabbar-icons .link {
  height: 100%;
  justify-content: center;
  align-items: center;
}
.tabbar-icons .tabbar-label {
  display: block;
  line-height: 1;
  margin: 0;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: var(--f7-tabbar-label-font-size);
  text-transform: var(--f7-tabbar-label-text-transform);
  font-weight: var(--f7-tabbar-label-font-weight);
  letter-spacing: var(--f7-tabbar-label-letter-spacing);
}
@media (min-width: 768px) and (min-height: 600px) {
  :root {
    --f7-tabbar-icons-height: var(--f7-tabbar-icons-tablet-height);
    --f7-tabbar-label-font-size: var(--f7-tabbar-label-tablet-font-size);
  }
}
.tabbar-scrollable .toolbar-inner {
  justify-content: flex-start;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  scrollbar-color: transparent;
}
.tabbar-scrollable .toolbar-inner::-webkit-scrollbar {
  -webkit-appearance: none;
          appearance: none;
  width: 0px;
  display: none;
  opacity: 0;
  background: transparent;
}
.tabbar-scrollable .toolbar-inner::-webkit-scrollbar-track {
  box-shadow: none;
  background: transparent;
}
.tabbar-scrollable .toolbar-inner::-webkit-scrollbar-thumb {
  background-color: transparent;
  outline: none;
  border-radius: 9999px;
  position: relative;
}
.tabbar-scrollable .tab-link,
.tabbar-scrollable .link {
  width: auto;
  flex-shrink: 0;
}
.toolbar-transitioning,
.navbar-transitioning + .toolbar,
.navbar-transitioning ~ * .toolbar {
  transition-duration: var(--f7-toolbar-hide-show-transition-duration);
}
.toolbar-bottom ~ *,
.ios .toolbar-bottom-ios ~ *,
.md .toolbar-bottom-md ~ * {
  --f7-page-toolbar-bottom-offset: var(--f7-toolbar-height);
}
.toolbar-bottom.tabbar-icons ~ *,
.ios .toolbar-bottom-ios.tabbar-icons ~ *,
.md .toolbar-bottom-md.tabbar-icons ~ * {
  --f7-page-toolbar-bottom-offset: var(--f7-tabbar-icons-height);
}
.toolbar-bottom.toolbar-hidden,
.ios .toolbar-bottom-ios.toolbar-hidden,
.md .toolbar-bottom-md.toolbar-hidden {
  transform: translate3d(0, 100%, 0);
}
.toolbar-top ~ *,
.ios .toolbar-top-ios ~ *,
.md .toolbar-top-md ~ * {
  --f7-page-toolbar-top-offset: var(--f7-toolbar-height);
}
.toolbar-top.tabbar-icons ~ *,
.ios .toolbar-top-ios.tabbar-icons ~ *,
.md .toolbar-top-md.tabbar-icons ~ * {
  --f7-page-toolbar-top-offset: var(--f7-tabbar-icons-height);
}
.toolbar-top.toolbar-hidden,
.ios .toolbar-top-ios.toolbar-hidden,
.md .toolbar-top-md.toolbar-hidden {
  transform: translate3d(0, -100%, 0);
}
.navbar ~ .toolbar-top,
.navbars ~ .toolbar-top,
.ios .navbar ~ .toolbar-top-ios,
.ios .navbars ~ .toolbar-top-ios,
.md .navbar ~ .toolbar-top-md,
.navbar ~ * .toolbar-top,
.navbars ~ * .toolbar-top,
.ios .navbar ~ * .toolbar-top-ios,
.ios .navbars ~ * .toolbar-top-ios,
.md .navbar ~ * .toolbar-top-md,
.navbar ~ .page:not(.no-navbar) .toolbar-top,
.navbars ~ .page:not(.no-navbar) .toolbar-top,
.ios .navbar ~ .page:not(.no-navbar) .toolbar-top-ios,
.ios .navbars ~ .page:not(.no-navbar) .toolbar-top-ios,
.md .navbar ~ .page:not(.no-navbar) .toolbar-top-md {
  top: calc(var(--f7-navbar-height) + var(--f7-safe-area-top));
}
.navbar ~ .toolbar-top.toolbar-hidden,
.navbars ~ .toolbar-top.toolbar-hidden,
.ios .navbar ~ .toolbar-top-ios.toolbar-hidden,
.ios .navbars ~ .toolbar-top-ios.toolbar-hidden,
.md .navbar ~ .toolbar-top-md.toolbar-hidden,
.navbar ~ * .toolbar-top.toolbar-hidden,
.navbars ~ * .toolbar-top.toolbar-hidden,
.ios .navbar ~ * .toolbar-top-ios.toolbar-hidden,
.ios .navbars ~ * .toolbar-top-ios.toolbar-hidden,
.md .navbar ~ * .toolbar-top-md.toolbar-hidden,
.navbar ~ .page:not(.no-navbar) .toolbar-top.toolbar-hidden,
.navbars ~ .page:not(.no-navbar) .toolbar-top.toolbar-hidden,
.ios .navbar ~ .page:not(.no-navbar) .toolbar-top-ios.toolbar-hidden,
.ios .navbars ~ .page:not(.no-navbar) .toolbar-top-ios.toolbar-hidden,
.md .navbar ~ .page:not(.no-navbar) .toolbar-top-md.toolbar-hidden {
  transform: translate3d(0, calc(-1 * (var(--f7-navbar-height) + var(--f7-toolbar-height) + var(--f7-safe-area-top))), 0);
}
.navbar ~ .toolbar-top.toolbar-hidden.tabbar-icons,
.navbars ~ .toolbar-top.toolbar-hidden.tabbar-icons,
.ios .navbar ~ .toolbar-top-ios.toolbar-hidden.tabbar-icons,
.ios .navbars ~ .toolbar-top-ios.toolbar-hidden.tabbar-icons,
.md .navbar ~ .toolbar-top-md.toolbar-hidden.tabbar-icons,
.navbar ~ * .toolbar-top.toolbar-hidden.tabbar-icons,
.navbars ~ * .toolbar-top.toolbar-hidden.tabbar-icons,
.ios .navbar ~ * .toolbar-top-ios.toolbar-hidden.tabbar-icons,
.ios .navbars ~ * .toolbar-top-ios.toolbar-hidden.tabbar-icons,
.md .navbar ~ * .toolbar-top-md.toolbar-hidden.tabbar-icons,
.navbar ~ .page:not(.no-navbar) .toolbar-top.toolbar-hidden.tabbar-icons,
.navbars ~ .page:not(.no-navbar) .toolbar-top.toolbar-hidden.tabbar-icons,
.ios .navbar ~ .page:not(.no-navbar) .toolbar-top-ios.toolbar-hidden.tabbar-icons,
.ios .navbars ~ .page:not(.no-navbar) .toolbar-top-ios.toolbar-hidden.tabbar-icons,
.md .navbar ~ .page:not(.no-navbar) .toolbar-top-md.toolbar-hidden.tabbar-icons {
  transform: translate3d(0, calc(-1 * (var(--f7-navbar-height) + var(--f7-tabbar-icons-height) + var(--f7-safe-area-top))), 0);
}
.navbar-hidden + .toolbar-top:not(.toolbar-hidden),
.ios .navbar-hidden + .toolbar-top-ios:not(.toolbar-hidden),
.md .navbar-hidden + .toolbar-top-md:not(.toolbar-hidden),
.navbar-hidden ~ * .toolbar-top:not(.toolbar-hidden),
.ios .navbar-hidden ~ * .toolbar-top-ios:not(.toolbar-hidden),
.md .navbar-hidden ~ * .toolbar-top-md:not(.toolbar-hidden) {
  transform: translate3d(0, calc(0px - var(--f7-navbar-height)), 0);
}
.navbar-large-hidden + .toolbar-top:not(.toolbar-hidden),
.ios .navbar-large-hidden + .toolbar-top-ios:not(.toolbar-hidden),
.md .navbar-large-hidden + .toolbar-top-md:not(.toolbar-hidden),
.navbar-large-hidden ~ * .toolbar-top:not(.toolbar-hidden),
.ios .navbar-large-hidden ~ * .toolbar-top-ios:not(.toolbar-hidden),
.md .navbar-large-hidden ~ * .toolbar-top-md:not(.toolbar-hidden) {
  transform: translate3d(0, calc(0px - var(--f7-navbar-height) - var(--f7-navbar-large-title-height)), 0);
}
.ios .toolbar a.icon-only {
  min-height: var(--f7-toolbar-height);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  min-width: 44px;
}
.ios .tabbar-icons .tab-link,
.ios .tabbar-icons .link {
  padding-top: 4px;
  padding-bottom: 4px;
}
.ios .tabbar-icons .tab-link i + span,
.ios .tabbar-icons .link i + span {
  margin: 0;
  margin-top: 4px;
}
@media (min-width: 768px) and (min-height: 600px) {
  .ios .tabbar .tab-link,
  .ios .tabbar-icons .tab-link,
  .ios .tabbar .link,
  .ios .tabbar-icons .link {
    justify-content: center;
    flex-direction: row;
  }
  .ios .tabbar .tab-link i + span,
  .ios .tabbar-icons .tab-link i + span,
  .ios .tabbar .link i + span,
  .ios .tabbar-icons .link i + span {
    margin-left: 5px;
  }
}
.ios .tabbar-scrollable .toolbar-inner {
  justify-content: flex-start;
}
.ios .tabbar-scrollable .tab-link,
.ios .tabbar-scrollable .link {
  padding: 0 8px;
}
.md .toolbar .link {
  justify-content: center;
  padding: 0 12px;
  min-width: 48px;
}
.md .toolbar a.icon-only {
  min-width: 0;
  flex-shrink: 0;
}
.md .tabbar .tab-link,
.md .tabbar-icons .tab-link,
.md .tabbar .link,
.md .tabbar-icons .link {
  padding-left: 0;
  padding-right: 0;
}
.md .tabbar a.icon-only,
.md .tabbar-icons a.icon-only {
  flex-shrink: initial;
}
.md .tabbar .tab-link,
.md .tabbar-icons .tab-link {
  transition-duration: 300ms;
  overflow: hidden;
  position: relative;
}
.md .tabbar i.icon,
.md .tabbar-icons i.icon {
  position: relative;
}
.md .tabbar i.icon::before,
.md .tabbar-icons i.icon::before {
  content: '';
  width: 64px;
  height: 32px;
  border-radius: 32px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%) scaleX(0.5);
  background: var(--f7-tabbar-link-active-icon-bg-color);
  opacity: 0;
  transition-duration: 200ms;
  z-index: -1;
}
.md .tabbar i.icon + .tabbar-label,
.md .tabbar-icons i.icon + .tabbar-label {
  margin-top: 8px;
}
.md .tabbar .tab-link-active i.icon::before,
.md .tabbar-icons .tab-link-active i.icon::before {
  opacity: 1;
  transform: translateX(-50%) translateY(-50%) scaleX(1);
}
.md .tabbar-icons .tab-link,
.md .tabbar-icons .link {
  padding-top: 8px;
  padding-bottom: 8px;
}
.md .tabbar-label {
  max-width: 100%;
  overflow: hidden;
  line-height: 1.2;
}
.md .tabbar-scrollable .toolbar-inner {
  overflow: auto;
}
.md .tabbar-scrollable .tab-link,
.md .tabbar-scrollable .link {
  padding: 0 12px;
}
/* === Subnavbar === */
:root {
  /*
  --f7-subnavbar-bg-color: var(--f7-bars-bg-color);
  --f7-subnavbar-bg-color-rgb: var(--f7-bars-bg-color-rgb);
  --f7-subnavbar-border-color: var(--f7-bars-border-color);
  --f7-subnavbar-link-color: var(--f7-bars-link-color);
  --f7-subnavbar-text-color: var(--f7-bars-text-color);
  */
  --f7-subnavbar-title-line-height: 1.2;
}
.ios {
  --f7-subnavbar-height: 44px;
  --f7-subnavbar-inner-padding-left: 8px;
  --f7-subnavbar-inner-padding-right: 8px;
  --f7-subnavbar-title-font-size: 34px;
  --f7-subnavbar-title-font-weight: 700;
  --f7-subnavbar-title-letter-spacing: -0.03em;
  --f7-subnavbar-title-margin-left: 8px;
  /*
  --f7-subnavbar-link-height: var(--f7-subnavbar-height);
  --f7-subnavbar-link-line-height: var(--f7-subnavbar-height);
  */
}
.md {
  --f7-subnavbar-height: 64px;
  --f7-subnavbar-inner-padding-left: 16px;
  --f7-subnavbar-inner-padding-right: 16px;
  --f7-subnavbar-title-font-size: 22px;
  --f7-subnavbar-title-font-weight: 400;
  --f7-subnavbar-title-letter-spacing: 0;
  --f7-subnavbar-title-margin-left: 0px;
  /*
  --f7-subnavbar-link-height: var(--f7-subnavbar-height);
  --f7-subnavbar-link-line-height: var(--f7-subnavbar-height);
  */
}
.subnavbar {
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 600;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--f7-subnavbar-bg-color, var(--f7-bars-bg-color));
  color: var(--f7-subnavbar-text-color, var(--f7-bars-text-color));
}
@supports ((-webkit-backdrop-filter: blur(20px)) or (backdrop-filter: blur(20px))) {
  .ios-translucent-bars .subnavbar {
    background-color: rgba(var(--f7-subnavbar-bg-color-rgb, var(--f7-bars-bg-color-rgb)), var(--f7-bars-translucent-opacity));
    -webkit-backdrop-filter: saturate(180%) blur(var(--f7-bars-translucent-blur));
            backdrop-filter: saturate(180%) blur(var(--f7-bars-translucent-blur));
  }
}
.subnavbar .subnavbar-title {
  position: relative;
  overflow: hidden;
  text-overflow: ellpsis;
  white-space: nowrap;
  font-size: var(--f7-subnavbar-title-font-size);
  font-weight: var(--f7-subnavbar-title-font-weight);
  text-align: left;
  display: inline-block;
  line-height: var(--f7-subnavbar-title-line-height);
  letter-spacing: var(--f7-subnavbar-title-letter-spacing);
  margin-left: var(--f7-subnavbar-title-margin-left);
}
.subnavbar .left,
.subnavbar .right {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.subnavbar .right:first-child {
  position: absolute;
  height: 100%;
}
.subnavbar a {
  color: var(--f7-subnavbar-link-color, var(--f7-bars-link-color, var(--f7-theme-color)));
}
.subnavbar a.link {
  line-height: var(--f7-subnavbar-link-line-height, var(--f7-subnavbar-height));
  height: var(--f7-subnavbar-link-height, var(--f7-subnavbar-height));
}
.subnavbar a.icon-only {
  min-width: var(--f7-subnavbar-height);
}
.subnavbar.no-outline:after {
  display: none !important;
}
.subnavbar.navbar-hidden:before {
  display: none !important;
}
.subnavbar:after,
.subnavbar:before {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.subnavbar:after {
  content: '';
  position: absolute;
  background-color: var(--f7-subnavbar-border-color, var(--f7-bars-border-color));
  display: block;
  z-index: 15;
  top: auto;
  right: auto;
  bottom: 0;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 100%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
.subnavbar-inner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  justify-content: space-between;
  overflow: hidden;
  padding: 0 calc(var(--f7-subnavbar-inner-padding-left) + var(--f7-safe-area-right)) 0 calc(var(--f7-subnavbar-inner-padding-right) + var(--f7-safe-area-left));
}
.navbar .subnavbar {
  top: 100%;
}
.views > .subnavbar,
.view > .subnavbar,
.page > .subnavbar {
  position: absolute;
}
.navbar ~ * .subnavbar,
.navbars ~ * .subnavbar,
.page-with-subnavbar .navbar ~ .subnavbar,
.page-with-subnavbar .navbar ~ * .subnavbar,
.navbar ~ .page-with-subnavbar:not(.no-navbar) .subnavbar,
.navbar ~ .subnavbar,
.navbars ~ .page-with-subnavbar:not(.no-navbar) .subnavbar,
.navbars ~ .subnavbar {
  top: calc(var(--f7-navbar-height) + var(--f7-safe-area-top));
}
.navbar ~ .page-with-navbar-large:not(.no-navbar) .subnavbar,
.navbars ~ .page-with-navbar-large:not(.no-navbar) .subnavbar,
.page-with-subnavbar.page-with-navbar-large .navbar ~ .subnavbar,
.page-with-subnavbar.page-with-navbar-large .navbar ~ * .subnavbar,
.navbar .title-large ~ .subnavbar {
  top: calc(var(--f7-navbar-height) + var(--f7-navbar-large-title-height) + var(--f7-safe-area-top));
  transform: translate3d(0, calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)), 0);
}
.page-with-subnavbar,
.subnavbar ~ * {
  --f7-page-subnavbar-offset: var(--f7-subnavbar-height);
}
.ios .subnavbar {
  height: calc(var(--f7-subnavbar-height) + 1px);
  margin-top: -1px;
  padding-top: 1px;
}
.ios .subnavbar .title {
  align-self: flex-start;
  flex-shrink: 10;
}
.ios .subnavbar .left a + a,
.ios .subnavbar .right a + a {
  margin-left: 16px;
}
.ios .subnavbar .left {
  margin-right: 10px;
}
.ios .subnavbar .right {
  margin-left: 10px;
}
.ios .subnavbar .right:first-child {
  right: 8px;
}
.ios .subnavbar a.link {
  justify-content: flex-start;
}
.ios .subnavbar a.icon-only {
  justify-content: center;
  margin: 0;
}
.md .subnavbar {
  height: var(--f7-subnavbar-height);
}
.md .subnavbar .right {
  margin-left: auto;
}
.md .subnavbar .right:first-child {
  right: 16px;
}
.md .subnavbar a.link {
  justify-content: center;
  padding: 0 12px;
}
.md .subnavbar a.icon-only {
  flex-shrink: 0;
  padding: 0 12px;
}
.md .subnavbar-inner > a.link:first-child {
  margin-left: calc(-1 * var(--f7-subnavbar-inner-padding-left));
}
.md .subnavbar-inner > a.link:last-child {
  margin-right: calc(-1 * var(--f7-subnavbar-inner-padding-right));
}
/* === Content Block === */
:root {
  --f7-block-padding-horizontal: 16px;
  --f7-block-padding-vertical: 16px;
  --f7-block-font-size: inherit;
  --f7-block-header-margin: 10px;
  --f7-block-footer-margin: 10px;
  --f7-block-header-font-size: 14px;
  --f7-block-footer-font-size: 14px;
  --f7-block-title-text-transform: none;
  --f7-block-title-white-space: nowrap;
  --f7-block-title-medium-text-transform: none;
  --f7-block-title-large-text-transform: none;
  --f7-block-inset-side-margin: 16px;
}
:root .dark,
:root.dark {
  --f7-block-outline-border-color: rgba(255, 255, 255, 0.15);
}
.ios {
  --f7-block-text-color: inherit;
  --f7-block-margin-vertical: 35px;
  --f7-block-outline-border-color: rgba(0, 0, 0, 0.22);
  --f7-block-title-font-size: 16px;
  --f7-block-title-font-weight: 600;
  --f7-block-title-line-height: 20px;
  --f7-block-title-margin-bottom: 10px;
  --f7-block-title-medium-font-size: 22px;
  --f7-block-title-medium-font-weight: bold;
  --f7-block-title-medium-line-height: 1.4;
  --f7-block-title-large-font-size: 30px;
  --f7-block-title-large-font-weight: bold;
  --f7-block-title-large-line-height: 1.3;
  --f7-block-inset-border-radius: 8px;
  --f7-block-title-text-color: #000;
  --f7-block-strong-text-color: #000;
  --f7-block-header-text-color: rgba(0, 0, 0, 0.45);
  --f7-block-footer-text-color: rgba(0, 0, 0, 0.45);
  --f7-block-strong-bg-color: #fff;
  --f7-block-title-medium-text-color: #000;
  --f7-block-title-large-text-color: #000;
}
.ios .dark,
.ios.dark {
  --f7-block-title-text-color: #fff;
  --f7-block-header-text-color: rgba(255, 255, 255, 0.55);
  --f7-block-footer-text-color: rgba(255, 255, 255, 0.55);
  --f7-block-strong-text-color: #fff;
  --f7-block-strong-bg-color: #1c1c1d;
  --f7-block-title-medium-text-color: #fff;
  --f7-block-title-large-text-color: #fff;
}
.md {
  --f7-block-margin-vertical: 32px;
  --f7-block-title-font-size: inherit;
  --f7-block-title-font-weight: 500;
  --f7-block-title-line-height: 16px;
  --f7-block-title-margin-bottom: 16px;
  --f7-block-title-medium-font-size: 16px;
  --f7-block-title-medium-font-weight: 500;
  --f7-block-title-medium-line-height: 1.3;
  --f7-block-title-large-font-size: 22px;
  --f7-block-title-large-font-weight: 500;
  --f7-block-title-large-line-height: 1.2;
  --f7-block-inset-border-radius: 16px;
}
.md,
.md .dark,
.md [class*='color-'] {
  --f7-block-text-color: var(--f7-md-on-surface);
  --f7-block-strong-text-color: var(--f7-md-on-surface);
  --f7-block-outline-border-color: var(--f7-md-outline);
  --f7-block-title-text-color: var(--f7-theme-color);
  --f7-block-title-medium-text-color: var(--f7-theme-color);
  --f7-block-title-large-text-color: var(--f7-theme-color);
  --f7-block-strong-bg-color: var(--f7-md-surface-1);
  --f7-block-header-text-color: var(--f7-md-on-surface-variant);
  --f7-block-footer-text-color: var(--f7-md-on-surface-variant);
}
.block {
  box-sizing: border-box;
  position: relative;
  z-index: 1;
  color: var(--f7-block-text-color);
  margin: var(--f7-block-margin-vertical) 0;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: calc(var(--f7-block-padding-horizontal) + var(--f7-safe-area-left));
  padding-right: calc(var(--f7-block-padding-horizontal) + var(--f7-safe-area-right));
  font-size: var(--f7-block-font-size);
}
.block > h1:first-child,
.block > h2:first-child,
.block > h3:first-child,
.block > h4:first-child,
.block > p:first-child {
  margin-top: 0;
}
.block > h1:last-child,
.block > h2:last-child,
.block > h3:last-child,
.block > h4:last-child,
.block > p:last-child {
  margin-bottom: 0;
}
.block-strong,
.ios .block-strong-ios,
.md .block-strong-md {
  color: var(--f7-block-strong-text-color);
  padding-top: var(--f7-block-padding-vertical);
  padding-bottom: var(--f7-block-padding-vertical);
  background-color: var(--f7-block-strong-bg-color);
}
.block-outline:not(.inset):before,
.ios .block-outline-ios:not(.inset):not(.inset-ios):before,
.md .block-outline-md:not(.inset):not(.inset-md):before {
  content: '';
  position: absolute;
  background-color: var(--f7-block-outline-border-color);
  display: block;
  z-index: 15;
  top: 0;
  right: auto;
  bottom: auto;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 0%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
.block-outline:not(.inset):after,
.ios .block-outline-ios:not(.inset):not(.inset-ios):after,
.md .block-outline-md:not(.inset):not(.inset-md):after {
  content: '';
  position: absolute;
  background-color: var(--f7-block-outline-border-color);
  display: block;
  z-index: 15;
  top: auto;
  right: auto;
  bottom: 0;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 100%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
.block-outline.inset,
.ios .block-outline-ios.inset,
.ios .block-outline-ios.inset-ios,
.md .block-outline-md.inset,
.md .block-outline-md.inset-md {
  border: 1px solid var(--f7-block-outline-border-color);
}
.block-title {
  position: relative;
  overflow: hidden;
  margin: 0;
  white-space: var(--f7-block-title-white-space);
  text-overflow: ellipsis;
  text-transform: var(--f7-block-title-text-transform);
  color: var(--f7-block-title-text-color);
  font-size: var(--f7-block-title-font-size, inherit);
  font-weight: var(--f7-block-title-font-weight);
  line-height: var(--f7-block-title-line-height);
  margin-top: var(--f7-block-margin-vertical);
  margin-bottom: var(--f7-block-title-margin-bottom);
  margin-left: calc(var(--f7-block-padding-horizontal) + var(--f7-safe-area-left));
  margin-right: calc(var(--f7-block-padding-horizontal) + var(--f7-safe-area-right));
}
.block-title + .list,
.block-title + .block,
.block-title + .card,
.block-title + .timeline,
.block-title + .block-header {
  margin-top: 0px;
}
.block-title-medium {
  font-size: var(--f7-block-title-medium-font-size);
  text-transform: var(--f7-block-title-medium-text-transform);
  color: var(--f7-block-title-medium-text-color);
  font-weight: var(--f7-block-title-medium-font-weight);
  line-height: var(--f7-block-title-medium-line-height);
}
.block-title-large {
  font-size: var(--f7-block-title-large-font-size);
  text-transform: var(--f7-block-title-large-text-transform);
  color: var(--f7-block-title-large-text-color);
  font-weight: var(--f7-block-title-large-font-weight);
  line-height: var(--f7-block-title-large-line-height);
}
.block > .block-title:first-child,
.list > .block-title:first-child {
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
}
.block-header {
  color: var(--f7-block-header-text-color);
  font-size: var(--f7-block-header-font-size);
  margin-bottom: var(--f7-block-header-margin);
  margin-top: var(--f7-block-margin-vertical);
}
.block-header + .list,
.block-header + .block,
.block-header + .card,
.block-header + .timeline {
  margin-top: var(--f7-block-header-margin);
}
.block-footer {
  color: var(--f7-block-footer-text-color);
  font-size: var(--f7-block-footer-font-size);
  margin-top: var(--f7-block-footer-margin);
  margin-bottom: var(--f7-block-margin-vertical);
}
.block-footer,
.block-header {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: calc(var(--f7-block-padding-horizontal) + var(--f7-safe-area-left));
  padding-right: calc(var(--f7-block-padding-horizontal) + var(--f7-safe-area-right));
}
.block-footer ul:first-child,
.block-header ul:first-child,
.block-footer p:first-child,
.block-header p:first-child,
.block-footer h1:first-child,
.block-header h1:first-child,
.block-footer h2:first-child,
.block-header h2:first-child,
.block-footer h3:first-child,
.block-header h3:first-child,
.block-footer h4:first-child,
.block-header h4:first-child {
  margin-top: 0;
}
.block-footer ul:last-child,
.block-header ul:last-child,
.block-footer p:last-child,
.block-header p:last-child,
.block-footer h1:last-child,
.block-header h1:last-child,
.block-footer h2:last-child,
.block-header h2:last-child,
.block-footer h3:last-child,
.block-header h3:last-child,
.block-footer h4:last-child,
.block-header h4:last-child {
  margin-bottom: 0;
}
.block-footer ul:first-child:last-child,
.block-header ul:first-child:last-child,
.block-footer p:first-child:last-child,
.block-header p:first-child:last-child,
.block-footer h1:first-child:last-child,
.block-header h1:first-child:last-child,
.block-footer h2:first-child:last-child,
.block-header h2:first-child:last-child,
.block-footer h3:first-child:last-child,
.block-header h3:first-child:last-child,
.block-footer h4:first-child:last-child,
.block-header h4:first-child:last-child {
  margin-top: 0;
  margin-bottom: 0;
}
.list .block-header,
.block .block-header,
.card .block-header,
.timeline .block-header {
  margin-top: 0;
}
.list .block-footer,
.block .block-footer,
.card .block-footer,
.timeline .block-footer {
  margin-bottom: 0;
}
.list + .block-footer,
.block + .block-footer,
.card + .block-footer,
.timeline + .block-footer {
  margin-top: calc(-1 * (var(--f7-block-margin-vertical) - var(--f7-block-footer-margin)));
}
.block + .block-footer {
  margin-top: calc(-1 * (var(--f7-block-margin-vertical) - var(--f7-block-footer-margin)));
  margin-bottom: var(--f7-block-margin-vertical);
}
.block .block-header,
.block .block-footer {
  padding: 0;
}
.block.inset,
.ios .block.inset-ios,
.md .block.inset-md {
  border-radius: var(--f7-block-inset-border-radius);
  margin-left: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-left));
  margin-right: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-right));
  --f7-safe-area-left: 0px;
  --f7-safe-area-right: 0px;
}
.block-strong.inset:before,
.ios .block-strong-ios.inset:before,
.md .block-strong-md.inset:before,
.ios .block-strong.inset-ios:before,
.md .block-strong.inset-md:before,
.ios .block-strong-ios.inset-ios:before,
.md .block-strong-md.inset-md:before,
.block-strong.inset:after,
.ios .block-strong-ios.inset:after,
.md .block-strong-md.inset:after,
.ios .block-strong.inset-ios:after,
.md .block-strong.inset-md:after,
.ios .block-strong-ios.inset-ios:after,
.md .block-strong-md.inset-md:after {
  display: none !important;
}
@media (min-width: 480px) {
  .block.xsmall-inset,
  .ios .block.xsmall-inset-ios,
  .md .block.xsmall-inset-md {
    border-radius: var(--f7-block-inset-border-radius);
    margin-left: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-left));
    margin-right: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-right));
    --f7-safe-area-left: 0px;
    --f7-safe-area-right: 0px;
  }
  .block.xsmall-inset:before,
  .ios .block.xsmall-inset-ios:before,
  .md .block.xsmall-inset-md:before,
  .block.xsmall-inset:after,
  .ios .block.xsmall-inset-ios:after,
  .md .block.xsmall-inset-md:after {
    display: none !important;
  }
  .block.xsmall-inset.block-outline,
  .ios .block.xsmall-inset-ios.block-outline,
  .ios .block.xsmall-inset-ios.block-outline-ios,
  .md .block.xsmall-inset-md.block-outline,
  .md .block.xsmall-inset-md.block-outline-md {
    border: 1px solid var(--f7-block-outline-border-color);
  }
}
@media (min-width: 568px) {
  .block.small-inset,
  .ios .block.small-inset-ios,
  .md .block.small-inset-md {
    border-radius: var(--f7-block-inset-border-radius);
    margin-left: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-left));
    margin-right: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-right));
    --f7-safe-area-left: 0px;
    --f7-safe-area-right: 0px;
  }
  .block.small-inset:before,
  .ios .block.small-inset-ios:before,
  .md .block.small-inset-md:before,
  .block.small-inset:after,
  .ios .block.small-inset-ios:after,
  .md .block.small-inset-md:after {
    display: none !important;
  }
  .block.small-inset.block-outline,
  .ios .block.small-inset-ios.block-outline,
  .ios .block.small-inset-ios.block-outline-ios,
  .md .block.small-inset-md.block-outline,
  .md .block.small-inset-md.block-outline-md {
    border: 1px solid var(--f7-block-outline-border-color);
  }
}
@media (min-width: 768px) {
  .block.medium-inset,
  .ios .block.medium-inset-ios,
  .md .block.medium-inset-md {
    border-radius: var(--f7-block-inset-border-radius);
    margin-left: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-left));
    margin-right: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-right));
    --f7-safe-area-left: 0px;
    --f7-safe-area-right: 0px;
  }
  .block.medium-inset:before,
  .ios .block.medium-inset-ios:before,
  .md .block.medium-inset-md:before,
  .block.medium-inset:after,
  .ios .block.medium-inset-ios:after,
  .md .block.medium-inset-md:after {
    display: none !important;
  }
  .block.medium-inset.block-outline,
  .ios .block.medium-inset-ios.block-outline,
  .ios .block.medium-inset-ios.block-outline-ios,
  .md .block.medium-inset-md.block-outline,
  .md .block.medium-inset-md.block-outline-md {
    border: 1px solid var(--f7-block-outline-border-color);
  }
}
@media (min-width: 1024px) {
  .block.large-inset,
  .ios .block.large-inset-ios,
  .md .block.large-inset-md {
    border-radius: var(--f7-block-inset-border-radius);
    margin-left: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-left));
    margin-right: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-right));
    --f7-safe-area-left: 0px;
    --f7-safe-area-right: 0px;
  }
  .block.large-inset:before,
  .ios .block.large-inset-ios:before,
  .md .block.large-inset-md:before,
  .block.large-inset:after,
  .ios .block.large-inset-ios:after,
  .md .block.large-inset-md:after {
    display: none !important;
  }
  .block.large-inset.block-outline,
  .ios .block.large-inset-ios.block-outline,
  .ios .block.large-inset-ios.block-outline-ios,
  .md .block.large-inset-md.block-outline,
  .md .block.large-inset-md.block-outline-md {
    border: 1px solid var(--f7-block-outline-border-color);
  }
}
@media (min-width: 1200px) {
  .block.xlarge-inset,
  .ios .block.xlarge-inset-ios,
  .md .block.xlarge-inset-md {
    border-radius: var(--f7-block-inset-border-radius);
    margin-left: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-left));
    margin-right: calc(var(--f7-block-inset-side-margin) + var(--f7-safe-area-outer-right));
    --f7-safe-area-left: 0px;
    --f7-safe-area-right: 0px;
  }
  .block.xlarge-inset:before,
  .ios .block.xlarge-inset-ios:before,
  .md .block.xlarge-inset-md:before,
  .block.xlarge-inset:after,
  .ios .block.xlarge-inset-ios:after,
  .md .block.xlarge-inset-md:after {
    display: none !important;
  }
  .block.xlarge-inset.block-outline,
  .ios .block.xlarge-inset-ios.block-outline,
  .ios .block.xlarge-inset-ios.block-outline-ios,
  .md .block.xlarge-inset-md.block-outline,
  .md .block.xlarge-inset-md.block-outline-md {
    border: 1px solid var(--f7-block-outline-border-color);
  }
}
/* === List View === */
:root {
  --f7-list-inset-side-margin: 16px;
  --f7-list-item-padding-horizontal: 16px;
  --f7-list-item-padding-vertical: 8px;
  --f7-list-media-item-padding-horizontal: 16px;
  --f7-list-item-text-max-lines: 2;
  --f7-list-chevron-icon-font-size: 20px;
  --f7-list-item-media-margin: 16px;
  --f7-list-item-title-font-size: inherit;
  --f7-list-item-title-font-weight: 400;
  --f7-list-item-title-line-height: inherit;
  --f7-list-item-title-white-space: nowrap;
  --f7-list-item-subtitle-font-weight: 400;
  --f7-list-item-subtitle-line-height: inherit;
  --f7-list-item-text-font-weight: 400;
  --f7-list-item-after-font-weight: 400;
  --f7-list-item-after-line-height: inherit;
  --f7-list-item-header-font-size: 12px;
  --f7-list-item-header-font-weight: 400;
  --f7-list-item-header-line-height: 1.2;
  --f7-list-item-footer-font-size: 12px;
  --f7-list-item-footer-font-weight: 400;
  --f7-list-item-footer-line-height: 1.2;
  /*
  --f7-list-button-text-color: var(--f7-theme-color);
  */
  --f7-list-button-font-size: inherit;
  --f7-list-button-font-weight: 400;
  --f7-list-button-text-align: center;
  --f7-list-group-title-line-height: inherit;
  --f7-menu-list-font-size: 14px;
  --f7-menu-list-item-title-font-size: 14px;
  --f7-menu-list-item-title-font-weight: 500;
  --f7-menu-list-item-subtitle-font-size: 14px;
  --f7-menu-list-item-text-font-size: 14px;
  --f7-menu-list-item-after-font-size: 14px;
  --f7-list-outline-border-color: rgba(0, 0, 0, 0.22);
  --f7-list-chevron-icon-color: rgba(0, 0, 0, 0.2);
}
:root .dark,
:root.dark {
  --f7-list-button-border-color: rgba(255, 255, 255, 0.15);
  --f7-list-outline-border-color: rgba(255, 255, 255, 0.15);
  --f7-list-group-title-border-color: rgba(255, 255, 255, 0.15);
  --f7-list-chevron-icon-color: rgba(255, 255, 255, 0.3);
}
.ios {
  --f7-list-in-list-padding-left: 30px;
  --f7-list-inset-border-radius: 8px;
  --f7-list-margin-vertical: 35px;
  --f7-list-font-size: 17px;
  --f7-list-chevron-icon-area: 20px;
  --f7-list-item-title-text-color: inherit;
  --f7-list-item-subtitle-text-color: inherit;
  --f7-list-link-pressed-bg-color: rgba(0, 0, 0, 0.15);
  --f7-list-item-subtitle-font-size: 15px;
  --f7-list-item-text-font-size: 15px;
  --f7-list-item-text-line-height: 21px;
  --f7-list-item-after-font-size: inherit;
  --f7-list-item-after-padding: 5px;
  --f7-list-item-min-height: 44px;
  --f7-list-item-media-icons-margin: 5px;
  --f7-list-media-item-padding-vertical: 10px;
  --f7-list-media-item-title-font-weight: 600;
  --f7-list-button-border-color: rgba(0, 0, 0, 0.22);
  /*
  --f7-list-button-pressed-bg-color: rgba(var(--f7-theme-color-rgb), .15);
  */
  --f7-list-group-title-border-color: rgba(0, 0, 0, 0.22);
  --f7-list-group-title-height: 31px;
  --f7-list-group-title-font-size: inherit;
  --f7-list-group-title-font-weight: 400;
  --f7-menu-list-offset: 8px;
  --f7-menu-list-border-radius: 8px;
  --f7-menu-list-item-bg-color: transparent;
  --f7-menu-list-item-text-color: inherit;
  --f7-menu-list-item-min-height: 44px;
  --f7-list-item-border-color: rgba(0, 0, 0, 0.22);
  --f7-list-outline-inset-border-color: rgba(0, 0, 0, 0.22);
  --f7-list-strong-bg-color: #fff;
  --f7-list-item-after-text-color: rgba(0, 0, 0, 0.45);
  --f7-list-item-header-text-color: inherit;
  --f7-list-item-footer-text-color: rgba(0, 0, 0, 0.45);
  --f7-list-item-text-text-color: rgba(0, 0, 0, 0.45);
  --f7-list-group-title-text-color: rgba(0, 0, 0, 0.45);
  --f7-list-group-title-bg-color: #f7f7f7;
  --f7-menu-list-item-selected-text-color: var(--f7-theme-color);
  --f7-menu-list-item-selected-bg-color: rgba(var(--f7-theme-color-rgb), 0.15);
}
.ios .dark,
.ios.dark {
  --f7-list-item-border-color: rgba(255, 255, 255, 0.15);
  --f7-list-outline-inset-color: rgba(255, 255, 255, 0.15);
  --f7-list-strong-bg-color: #1c1c1d;
  --f7-list-item-after-text-color: rgba(255, 255, 255, 0.55);
  --f7-list-item-header-text-color: rgba(255, 255, 255, 0.55);
  --f7-list-item-footer-text-color: rgba(255, 255, 255, 0.55);
  --f7-list-item-text-text-color: rgba(255, 255, 255, 0.55);
  --f7-list-group-title-text-color: rgba(255, 255, 255, 0.55);
  --f7-list-group-title-bg-color: #232323;
  --f7-list-link-pressed-bg-color: rgba(255, 255, 255, 0.08);
  --f7-menu-list-item-selected-text-color: inherit;
  --f7-menu-list-item-selected-bg-color: var(--f7-theme-color);
}
.md {
  --f7-list-in-list-padding-left: 24px;
  --f7-list-inset-border-radius: 16px;
  --f7-list-margin-vertical: 32px;
  --f7-list-font-size: 16px;
  --f7-list-chevron-icon-area: 26px;
  --f7-list-item-subtitle-font-size: 14px;
  --f7-list-item-text-font-size: 14px;
  --f7-list-item-text-line-height: 20px;
  --f7-list-item-after-font-size: 14px;
  --f7-list-item-after-padding: 8px;
  --f7-list-item-min-height: 48px;
  --f7-list-item-media-icons-margin: 8px;
  --f7-list-media-item-padding-vertical: 12px;
  --f7-list-media-item-title-font-weight: 500;
  --f7-list-button-border-color: transparent;
  /*
  --f7-list-button-pressed-bg-color: transparent;
  */
  --f7-list-group-title-border-color: transparent;
  --f7-list-group-title-height: 48px;
  --f7-list-group-title-font-size: 16px;
  --f7-list-group-title-font-weight: 400;
  --f7-menu-list-offset: 16px;
  --f7-menu-list-border-radius: 999px;
  --f7-menu-list-item-min-height: 56px;
  --f7-list-link-pressed-bg-color: transparent;
}
.md,
.md .dark,
.md [class*='color-'] {
  --f7-list-outline-inset-border-color: var(--f7-md-outline);
  --f7-list-item-border-color: var(--f7-md-outline);
  --f7-list-item-title-text-color: var(--f7-md-on-surface);
  --f7-list-item-subtitle-text-color: var(--f7-md-on-surface);
  --f7-list-group-title-bg-color: var(--f7-md-surface-2);
  --f7-list-group-title-text-color: var(--f7-md-on-surface-variant);
  --f7-list-strong-bg-color: var(--f7-md-surface-1);
  --f7-list-item-after-text-color: var(--f7-md-on-surface-variant);
  --f7-list-item-text-text-color: var(--f7-md-on-surface-variant);
  --f7-list-item-header-text-color: var(--f7-md-on-surface-variant);
  --f7-list-item-footer-text-color: var(--f7-md-on-surface-variant);
  --f7-menu-list-item-text-color: var(--f7-md-on-surface-variant);
  --f7-menu-list-item-bg-color: var(--f7-md-surface-1);
  --f7-menu-list-item-selected-text-color: var(--f7-md-on-secondary-container);
  --f7-menu-list-item-selected-bg-color: var(--f7-md-secondary-container);
}
:root {
  --f7-list-chevron-icon-left: 'chevron_left';
  --f7-list-chevron-icon-right: 'chevron_right';
}
.list {
  position: relative;
  z-index: 1;
  font-size: var(--f7-list-font-size);
  margin: var(--f7-list-margin-vertical) 0;
}
.list-strong ul,
.ios .list-strong-ios ul,
.md .list-strong-md ul {
  background: var(--f7-list-strong-bg-color);
}
.list-outline ul:before,
.ios .list-outline-ios ul:before,
.md .list-outline-md ul:before {
  content: '';
  position: absolute;
  background-color: var(--f7-list-outline-border-color);
  display: block;
  z-index: 15;
  top: 0;
  right: auto;
  bottom: auto;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 0%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
.list-outline ul:after,
.ios .list-outline-ios ul:after,
.md .list-outline-md ul:after {
  content: '';
  position: absolute;
  background-color: var(--f7-list-outline-border-color);
  display: block;
  z-index: 15;
  top: auto;
  right: auto;
  bottom: 0;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 100%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
.list-outline.inset,
.ios .list-outline-ios.inset,
.ios .list-outline-ios.inset-ios,
.md .list-outline-md.inset,
.md .list-outline-md.inset-md {
  border: 1px solid var(--f7-list-outline-inset-border-color);
}
.list ul {
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
}
.list ul ul {
  padding-left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-list-in-list-padding-left));
}
.list ul ul:before,
.list ul ul:after {
  display: none !important;
}
.list li {
  position: relative;
  box-sizing: border-box;
}
.list .item-media {
  display: flex;
  flex-shrink: 0;
  flex-wrap: nowrap;
  align-items: center;
  box-sizing: border-box;
  padding-bottom: var(--f7-list-item-padding-vertical);
  padding-top: var(--f7-list-item-padding-vertical);
  position: relative;
}
.list .item-media + .item-inner {
  margin-left: var(--f7-list-item-media-margin);
}
.list .item-media i + i,
.list .item-media i + img {
  margin-left: var(--f7-list-item-media-icons-margin);
}
.list .item-after {
  padding-left: var(--f7-list-item-after-padding);
}
.list .item-inner {
  position: relative;
  width: 100%;
  min-width: 0;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
  align-self: stretch;
  padding-top: var(--f7-list-item-padding-vertical);
  padding-bottom: var(--f7-list-item-padding-vertical);
  min-height: calc(var(--f7-list-item-min-height));
  padding-right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
}
.list .item-title {
  min-width: 0;
  flex-shrink: 1;
  white-space: var(--f7-list-item-title-white-space);
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  font-size: var(--f7-list-item-title-font-size);
  font-weight: var(--f7-list-item-title-font-weight);
  color: var(--f7-list-item-title-text-color);
  line-height: var(--f7-list-item-title-line-height);
}
.list .item-after {
  white-space: nowrap;
  flex-shrink: 0;
  display: flex;
  font-size: var(--f7-list-item-after-font-size);
  font-weight: var(--f7-list-item-after-font-weight);
  color: var(--f7-list-item-after-text-color);
  line-height: var(--f7-list-item-after-line-height);
  margin-left: auto;
}
.list .item-header,
.list .item-footer {
  white-space: normal;
}
.list .item-header {
  color: var(--f7-list-item-header-text-color);
  font-size: var(--f7-list-item-header-font-size);
  font-weight: var(--f7-list-item-header-font-weight);
  line-height: var(--f7-list-item-header-line-height);
}
.list .item-footer {
  color: var(--f7-list-item-footer-text-color);
  font-size: var(--f7-list-item-footer-font-size);
  font-weight: var(--f7-list-item-footer-font-weight);
  line-height: var(--f7-list-item-footer-line-height);
}
.list .item-link,
.list label.item-content,
.list .list-button {
  transition-duration: 300ms;
  transition-property: background-color, color;
  position: relative;
  overflow: hidden;
  z-index: 0;
  cursor: pointer;
}
.list .item-link,
.list .list-button {
  display: block;
}
.list .item-link,
.list label.item-content {
  color: inherit;
}
.list .item-link.active-state,
.list label.item-content.active-state {
  background-color: var(--f7-list-link-pressed-bg-color);
}
.list .item-link .item-inner {
  padding-right: calc(var(--f7-list-chevron-icon-area) + var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
}
.list .item-content {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
  min-height: calc(var(--f7-list-item-min-height));
  padding-left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
}
.list .item-subtitle {
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  max-width: 100%;
  text-overflow: ellipsis;
  font-size: var(--f7-list-item-subtitle-font-size);
  font-weight: var(--f7-list-item-subtitle-font-weight);
  color: var(--f7-list-item-subtitle-text-color);
  line-height: var(--f7-list-item-subtitle-line-height);
}
.list .item-text {
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  /* autoprefixer: ignore next */
  -webkit-line-clamp: var(--f7-list-item-text-max-lines);
  /* autoprefixer: ignore next */
  -webkit-box-orient: vertical;
  display: -webkit-box;
  font-size: var(--f7-list-item-text-font-size);
  font-weight: var(--f7-list-item-text-font-weight);
  color: var(--f7-list-item-text-text-color);
  line-height: var(--f7-list-item-text-line-height);
  max-height: calc(var(--f7-list-item-text-line-height) * var(--f7-list-item-text-max-lines));
}
.list .item-title-row {
  position: relative;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
}
.list .item-title-row .item-after {
  align-self: center;
}
.list li:last-child .list-button:after {
  display: none !important;
}
.list li:last-child > .item-inner:after,
.list li:last-child li:last-child > .item-inner:after,
.list li:last-child > .item-content > .item-inner:after,
.list li:last-child li:last-child > .item-content > .item-inner:after,
.list li:last-child > .swipeout-content > .item-content > .item-inner:after,
.list li:last-child li:last-child > .swipeout-content > .item-content > .item-inner:after,
.list li:last-child > .item-link > .item-content > .item-inner:after,
.list li:last-child li:last-child > .item-link > .item-content > .item-inner:after {
  display: none !important;
}
.list li li:last-child .item-inner:after,
.list li:last-child li .item-inner:after {
  content: '';
  position: absolute;
  background-color: var(--f7-list-item-border-color);
  display: block;
  z-index: 15;
  top: auto;
  right: auto;
  bottom: 0;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 100%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
.list-button {
  --f7-touch-ripple-color: var(--f7-link-touch-ripple-color, rgba(var(--f7-theme-color-rgb), 0.25));
  padding: 0 var(--f7-list-item-padding-horizontal);
  line-height: var(--f7-list-item-min-height);
  color: var(--f7-list-button-text-color, var(--f7-theme-color));
  font-size: var(--f7-list-button-font-size);
  font-weight: var(--f7-list-button-font-weight);
  text-align: var(--f7-list-button-text-align);
}
.ios .list-button.active-state {
  background-color: var(--f7-list-button-pressed-bg-color, rgba(var(--f7-theme-color-rgb), 0.15));
}
.md .list-button.active-state {
  background-color: var(--f7-list-button-pressed-bg-color, transparent);
}
.list-dividers .list-button:after,
.ios .list-dividers-ios .list-button:after,
.md .list-dividers-md .list-button:after {
  content: '';
  position: absolute;
  background-color: var(--f7-list-button-border-color);
  display: block;
  z-index: 15;
  top: auto;
  right: auto;
  bottom: 0;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 100%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
.list-button[class*='color-'] {
  --f7-list-button-text-color: var(--f7-theme-color);
}
.simple-list li {
  position: relative;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-content: center;
  line-height: var(--f7-list-item-min-height);
  height: var(--f7-list-item-min-height);
  padding-left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
  padding-right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
}
.simple-list li:after {
  left: var(--f7-list-item-padding-horizontal);
  width: auto;
  left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
  right: 0;
}
.simple-list li:last-child:after {
  display: none !important;
}
.links-list li {
  z-index: 1;
}
.links-list a {
  transition-duration: 300ms;
  transition-property: background-color;
  display: block;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: space-between;
  box-sizing: border-box;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
  height: var(--f7-list-item-min-height);
  color: inherit;
}
.links-list a .ripple-wave {
  z-index: 0;
}
.links-list a:after {
  width: auto;
}
.links-list a.active-state {
  background-color: var(--f7-list-link-pressed-bg-color);
}
.links-list a {
  padding-left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
  padding-right: calc(var(--f7-list-chevron-icon-area) + var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
}
.links-list a:after {
  left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
  right: 0;
}
.links-list li:last-child a:after {
  display: none !important;
}
.list-dividers.simple-list li:after,
.ios .list-dividers-ios.simple-list li:after,
.md .list-dividers-md.simple-list li:after,
.list-dividers.links-list a:after,
.ios .list-dividers-ios.links-list a:after,
.md .list-dividers-md.links-list a:after,
.list-dividers .item-inner:after,
.ios .list-dividers-ios .item-inner:after,
.md .list-dividers-md .item-inner:after {
  content: '';
  position: absolute;
  background-color: var(--f7-list-item-border-color);
  display: block;
  z-index: 15;
  top: auto;
  right: auto;
  bottom: 0;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 100%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
.md .media-list:not(.inset):not(.inset-md):not(.menu-list) .item-content,
.md .media-list:not(.inset):not(.inset-md):not(.menu-list) li > .item-link,
.md .list:not(.inset):not(.inset-md):not(.menu-list) .media-item .item-content,
.md .list:not(.inset):not(.inset-md):not(.menu-list) .media-item > .item-link {
  margin-left: calc(var(--f7-list-item-padding-horizontal) / 2);
  margin-right: calc(var(--f7-list-item-padding-horizontal) / 2);
  border-radius: 16px;
}
.md .media-list:not(.inset):not(.inset-md):not(.menu-list) li > .item-link > .item-content,
.md .list:not(.inset):not(.inset-md):not(.menu-list) .media-item > .item-link > .item-content {
  margin-left: 0;
  margin-right: 0;
}
.md .media-list:not(.inset):not(.inset-md):not(.menu-list) .item-content,
.md .list:not(.inset):not(.inset-md):not(.menu-list) .media-item .item-content {
  padding-left: calc(var(--f7-list-item-padding-horizontal) / 2 + var(--f7-safe-area-left));
}
.media-list,
li.media-item {
  --f7-list-item-padding-vertical: var(--f7-list-media-item-padding-vertical);
  --f7-list-item-padding-horizontal: var(--f7-list-media-item-padding-horizontal);
}
.media-list .item-title,
li.media-item .item-title {
  font-weight: var(--f7-list-media-item-title-font-weight, var(--f7-list-item-title-font-weight, inherit));
}
.media-list .item-inner,
li.media-item .item-inner {
  display: block;
  align-self: stretch;
}
.media-list .item-media img,
li.media-item .item-media img {
  display: block;
}
.media-list .item-link .item-inner,
li.media-item .item-link .item-inner {
  padding-right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
}
.media-list .item-link .item-title-row,
li.media-item .item-link .item-title-row {
  padding-right: calc(var(--f7-list-chevron-icon-area));
}
.media-list.chevron-center .item-link .item-inner,
.media-list .chevron-center .item-link .item-inner,
.media-list .item-link.chevron-center .item-inner,
li.media-item.chevron-center .item-link .item-inner,
li.media-item .item-link.chevron-center .item-inner,
li.media-item .chevron-center .item-link .item-inner {
  padding-right: calc(var(--f7-list-chevron-icon-area) + var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
}
.media-list.chevron-center .item-title-row,
.media-list .chevron-center .item-title-row,
li.media-item.chevron-center .item-title-row,
li.media-item .chevron-center .item-title-row {
  padding-right: 0;
}
.list .item-link .item-inner:before,
.links-list a:before,
.media-list .item-link .item-title-row:before,
li.media-item .item-link .item-title-row:before,
.media-list.chevron-center .item-link .item-inner:before,
.media-list .chevron-center .item-link .item-inner:before,
.media-list .item-link.chevron-center .item-inner:before,
li.media-item.chevron-center .item-link .item-inner:before,
li.media-item .chevron-center .item-link .item-inner:before,
li.media-item .item-link.chevron-center .item-inner:before {
  font-family: 'framework7-core-icons';
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'liga';
  text-align: center;
  display: block;
  width: 100%;
  height: 100%;
  font-size: 20px;
  position: absolute;
  top: 50%;
  width: 8px;
  height: 14px;
  margin-top: -7px;
  font-size: var(--f7-list-chevron-icon-font-size);
  line-height: 14px;
  color: var(--f7-list-chevron-icon-color);
  pointer-events: none;
  right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
  content: var(--f7-list-chevron-icon-right);
}
.media-list.chevron-center .item-title-row:before,
.media-list .chevron-center .item-title-row:before,
li.media-item.chevron-center .item-title-row:before,
li.media-item .chevron-center .item-title-row:before {
  display: none;
}
.media-list .item-link .item-inner:before,
li.media-item .item-link .item-inner:before {
  display: none;
}
.media-list .item-link .item-title-row:before,
li.media-item .item-link .item-title-row:before {
  right: 0;
}
.list-group ul:after,
.list-group ul:before {
  z-index: 25 !important;
}
.list-group + .list-group ul:before {
  display: none !important;
}
li.list-group-title,
.list-group-title {
  margin-top: -1px;
  white-space: nowrap;
  position: relative;
  max-width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  z-index: 15;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
  padding-right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
  box-sizing: border-box;
  display: flex;
  align-items: center;
  align-content: center;
  height: var(--f7-list-group-title-height);
  color: var(--f7-list-group-title-text-color);
  font-size: var(--f7-list-group-title-font-size);
  font-weight: var(--f7-list-group-title-font-weight);
  background-color: var(--f7-list-group-title-bg-color);
  line-height: var(--f7-list-group-title-line-height);
}
li.list-group-title:before,
.list-group-title:before {
  content: '';
  position: absolute;
  background-color: var(--f7-list-group-title-border-color);
  display: block;
  z-index: 15;
  top: 0;
  right: auto;
  bottom: auto;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 0%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
li.list-group-title:after,
.list-group-title:after {
  display: none !important;
}
.list-group > .list-group-title,
.list-group > ul > .list-group-title {
  top: 0;
  margin-top: 0;
  position: sticky;
  z-index: 20;
}
.page-with-navbar-large .list-group > .list-group-title,
.page-with-navbar-large .list-group > ul > .list-group-title {
  top: calc(-1 * var(--f7-navbar-large-title-height));
}
.list.inset,
.ios .list.inset-ios,
.md .list.inset-md {
  margin-left: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-left));
  margin-right: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-right));
  border-radius: var(--f7-list-inset-border-radius);
  --f7-safe-area-left: 0px;
  --f7-safe-area-right: 0px;
}
.list.inset .block-title,
.ios .list.inset-ios .block-title,
.md .list.inset-md .block-title {
  margin-left: 0;
  margin-right: 0;
}
.list.inset ul,
.ios .list.inset-ios ul,
.md .list.inset-md ul {
  border-radius: var(--f7-list-inset-border-radius);
}
.list.inset ul:before,
.ios .list.inset-ios ul:before,
.md .list.inset-md ul:before,
.list.inset ul:after,
.ios .list.inset-ios ul:after,
.md .list.inset-md ul:after {
  display: none !important;
}
.list.inset li.swipeout:first-child,
.ios .list.inset-ios li.swipeout:first-child,
.md .list.inset-md li.swipeout:first-child,
.list.inset li:first-child > a,
.ios .list.inset-ios li:first-child > a,
.md .list.inset-md li:first-child > a {
  border-radius: var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius) 0 0;
}
.list.inset li.swipeout:last-child,
.ios .list.inset-ios li.swipeout:last-child,
.md .list.inset-md li.swipeout:last-child,
.list.inset li:last-child > a,
.ios .list.inset-ios li:last-child > a,
.md .list.inset-md li:last-child > a {
  border-radius: 0 0 var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius);
}
.list.inset li.swipeout:first-child:last-child,
.ios .list.inset-ios li.swipeout:first-child:last-child,
.md .list.inset-md li.swipeout:first-child:last-child,
.list.inset li:first-child:last-child > a,
.ios .list.inset-ios li:first-child:last-child > a,
.md .list.inset-md li:first-child:last-child > a {
  border-radius: var(--f7-list-inset-border-radius);
}
@media (min-width: 480px) {
  .list.xsmall-inset,
  .ios .list.xsmall-inset-ios,
  .md .list.xsmall-inset-md {
    margin-left: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-left));
    margin-right: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-right));
    border-radius: var(--f7-list-inset-border-radius);
    --f7-safe-area-left: 0px;
    --f7-safe-area-right: 0px;
  }
  .list.xsmall-inset .block-title,
  .ios .list.xsmall-inset-ios .block-title,
  .md .list.xsmall-inset-md .block-title {
    margin-left: 0;
    margin-right: 0;
  }
  .list.xsmall-inset ul,
  .ios .list.xsmall-inset-ios ul,
  .md .list.xsmall-inset-md ul {
    border-radius: var(--f7-list-inset-border-radius);
  }
  .list.xsmall-inset ul:before,
  .ios .list.xsmall-inset-ios ul:before,
  .md .list.xsmall-inset-md ul:before,
  .list.xsmall-inset ul:after,
  .ios .list.xsmall-inset-ios ul:after,
  .md .list.xsmall-inset-md ul:after {
    display: none !important;
  }
  .list.xsmall-inset li:first-child > a,
  .ios .list.xsmall-inset-ios li:first-child > a,
  .md .list.xsmall-inset-md li:first-child > a {
    border-radius: var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius) 0 0;
  }
  .list.xsmall-inset li:last-child > a,
  .ios .list.xsmall-inset-ios li:last-child > a,
  .md .list.xsmall-inset-md li:last-child > a {
    border-radius: 0 0 var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius);
  }
  .list.xsmall-inset li:first-child:last-child > a,
  .ios .list.xsmall-inset-ios li:first-child:last-child > a,
  .md .list.xsmall-inset-md li:first-child:last-child > a {
    border-radius: var(--f7-list-inset-border-radius);
  }
  .list.xsmall-inset.list-outline ul,
  .ios .list.xsmall-inset-ios.list-outline ul,
  .ios .list.xsmall-inset-ios.list-outline-ios ul,
  .md .list.xsmall-inset-md.list-outline ul,
  .md .list.xsmall-inset-md.list-outline-md ul {
    border: 1px solid var(--f7-list-outline-inset-border-color);
  }
}
@media (min-width: 568px) {
  .list.small-inset,
  .ios .list.small-inset-ios,
  .md .list.small-inset-md {
    margin-left: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-left));
    margin-right: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-right));
    border-radius: var(--f7-list-inset-border-radius);
    --f7-safe-area-left: 0px;
    --f7-safe-area-right: 0px;
  }
  .list.small-inset .block-title,
  .ios .list.small-inset-ios .block-title,
  .md .list.small-inset-md .block-title {
    margin-left: 0;
    margin-right: 0;
  }
  .list.small-inset ul,
  .ios .list.small-inset-ios ul,
  .md .list.small-inset-md ul {
    border-radius: var(--f7-list-inset-border-radius);
  }
  .list.small-inset ul:before,
  .ios .list.small-inset-ios ul:before,
  .md .list.small-inset-md ul:before,
  .list.small-inset ul:after,
  .ios .list.small-inset-ios ul:after,
  .md .list.small-inset-md ul:after {
    display: none !important;
  }
  .list.small-inset li:first-child > a,
  .ios .list.small-inset-ios li:first-child > a,
  .md .list.small-inset-md li:first-child > a {
    border-radius: var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius) 0 0;
  }
  .list.small-inset li:last-child > a,
  .ios .list.small-inset-ios li:last-child > a,
  .md .list.small-inset-md li:last-child > a {
    border-radius: 0 0 var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius);
  }
  .list.small-inset li:first-child:last-child > a,
  .ios .list.small-inset-ios li:first-child:last-child > a,
  .md .list.small-inset-md li:first-child:last-child > a {
    border-radius: var(--f7-list-inset-border-radius);
  }
  .list.small-inset.list-outline ul,
  .ios .list.small-inset-ios.list-outline ul,
  .ios .list.small-inset-ios.list-outline-ios ul,
  .md .list.small-inset-md.list-outline ul,
  .md .list.small-inset-md.list-outline-md ul {
    border: 1px solid var(--f7-list-outline-inset-border-color);
  }
}
@media (min-width: 768px) {
  .list.medium-inset,
  .ios .list.medium-inset-ios,
  .md .list.medium-inset-md {
    margin-left: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-left));
    margin-right: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-right));
    border-radius: var(--f7-list-inset-border-radius);
    --f7-safe-area-left: 0px;
    --f7-safe-area-right: 0px;
  }
  .list.medium-inset .block-title,
  .ios .list.medium-inset-ios .block-title,
  .md .list.medium-inset-md .block-title {
    margin-left: 0;
    margin-right: 0;
  }
  .list.medium-inset ul,
  .ios .list.medium-inset-ios ul,
  .md .list.medium-inset-md ul {
    border-radius: var(--f7-list-inset-border-radius);
  }
  .list.medium-inset ul:before,
  .ios .list.medium-inset-ios ul:before,
  .md .list.medium-inset-md ul:before,
  .list.medium-inset ul:after,
  .ios .list.medium-inset-ios ul:after,
  .md .list.medium-inset-md ul:after {
    display: none !important;
  }
  .list.medium-inset li:first-child > a,
  .ios .list.medium-inset-ios li:first-child > a,
  .md .list.medium-inset-md li:first-child > a {
    border-radius: var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius) 0 0;
  }
  .list.medium-inset li:last-child > a,
  .ios .list.medium-inset-ios li:last-child > a,
  .md .list.medium-inset-md li:last-child > a {
    border-radius: 0 0 var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius);
  }
  .list.medium-inset li:first-child:last-child > a,
  .ios .list.medium-inset-ios li:first-child:last-child > a,
  .md .list.medium-inset-md li:first-child:last-child > a {
    border-radius: var(--f7-list-inset-border-radius);
  }
  .list.medium-inset.list-outline ul,
  .ios .list.medium-inset-ios.list-outline ul,
  .ios .list.medium-inset-ios.list-outline-ios ul,
  .md .list.medium-inset-md.list-outline ul,
  .md .list.medium-inset-md.list-outline-md ul {
    border: 1px solid var(--f7-list-outline-inset-border-color);
  }
}
@media (min-width: 1024px) {
  .list.large-inset,
  .ios .list.large-inset-ios,
  .md .list.large-inset-md {
    margin-left: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-left));
    margin-right: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-right));
    border-radius: var(--f7-list-inset-border-radius);
    --f7-safe-area-left: 0px;
    --f7-safe-area-right: 0px;
  }
  .list.large-inset .block-title,
  .ios .list.large-inset-ios .block-title,
  .md .list.large-inset-md .block-title {
    margin-left: 0;
    margin-right: 0;
  }
  .list.large-inset ul,
  .ios .list.large-inset-ios ul,
  .md .list.large-inset-md ul {
    border-radius: var(--f7-list-inset-border-radius);
  }
  .list.large-inset ul:before,
  .ios .list.large-inset-ios ul:before,
  .md .list.large-inset-md ul:before,
  .list.large-inset ul:after,
  .ios .list.large-inset-ios ul:after,
  .md .list.large-inset-md ul:after {
    display: none !important;
  }
  .list.large-inset li:first-child > a,
  .ios .list.large-inset-ios li:first-child > a,
  .md .list.large-inset-md li:first-child > a {
    border-radius: var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius) 0 0;
  }
  .list.large-inset li:last-child > a,
  .ios .list.large-inset-ios li:last-child > a,
  .md .list.large-inset-md li:last-child > a {
    border-radius: 0 0 var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius);
  }
  .list.large-inset li:first-child:last-child > a,
  .ios .list.large-inset-ios li:first-child:last-child > a,
  .md .list.large-inset-md li:first-child:last-child > a {
    border-radius: var(--f7-list-inset-border-radius);
  }
  .list.large-inset.list-outline ul,
  .ios .list.large-inset-ios.list-outline ul,
  .ios .list.large-inset-ios.list-outline-ios ul,
  .md .list.large-inset-md.list-outline ul,
  .md .list.large-inset-md.list-outline-md ul {
    border: 1px solid var(--f7-list-outline-inset-border-color);
  }
}
@media (min-width: 1200px) {
  .list.xlarge-inset,
  .ios .list.xlarge-inset-ios,
  .md .list.xlarge-inset-md {
    margin-left: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-left));
    margin-right: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-right));
    border-radius: var(--f7-list-inset-border-radius);
    --f7-safe-area-left: 0px;
    --f7-safe-area-right: 0px;
  }
  .list.xlarge-inset .block-title,
  .ios .list.xlarge-inset-ios .block-title,
  .md .list.xlarge-inset-md .block-title {
    margin-left: 0;
    margin-right: 0;
  }
  .list.xlarge-inset ul,
  .ios .list.xlarge-inset-ios ul,
  .md .list.xlarge-inset-md ul {
    border-radius: var(--f7-list-inset-border-radius);
  }
  .list.xlarge-inset ul:before,
  .ios .list.xlarge-inset-ios ul:before,
  .md .list.xlarge-inset-md ul:before,
  .list.xlarge-inset ul:after,
  .ios .list.xlarge-inset-ios ul:after,
  .md .list.xlarge-inset-md ul:after {
    display: none !important;
  }
  .list.xlarge-inset li:first-child > a,
  .ios .list.xlarge-inset-ios li:first-child > a,
  .md .list.xlarge-inset-md li:first-child > a {
    border-radius: var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius) 0 0;
  }
  .list.xlarge-inset li:last-child > a,
  .ios .list.xlarge-inset-ios li:last-child > a,
  .md .list.xlarge-inset-md li:last-child > a {
    border-radius: 0 0 var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius);
  }
  .list.xlarge-inset li:first-child:last-child > a,
  .ios .list.xlarge-inset-ios li:first-child:last-child > a,
  .md .list.xlarge-inset-md li:first-child:last-child > a {
    border-radius: var(--f7-list-inset-border-radius);
  }
  .list.xlarge-inset.list-outline ul,
  .ios .list.xlarge-inset-ios.list-outline ul,
  .ios .list.xlarge-inset-ios.list-outline-ios ul,
  .md .list.xlarge-inset-md.list-outline ul,
  .md .list.xlarge-inset-md.list-outline-md ul {
    border: 1px solid var(--f7-list-outline-inset-border-color);
  }
}
.list.no-chevron,
.list .no-chevron,
.menu-list {
  --f7-list-chevron-icon-color: transparent;
  --f7-list-chevron-icon-area: 0px;
}
.menu-list {
  --f7-list-font-size: var(--f7-menu-list-font-size);
  --f7-list-item-title-font-size: var(--f7-menu-list-item-title-font-size);
  --f7-list-item-title-font-weight: var(--f7-menu-list-item-title-font-weight);
  --f7-list-item-subtitle-font-size: var(--f7-menu-list-item-subtitle-font-size);
  --f7-list-item-text-font-size: var(--f7-menu-list-item-text-font-size);
  --f7-list-item-after-font-size: var(--f7-menu-list-item-after-font-size);
  --f7-list-item-min-height: var(--f7-menu-list-item-min-height);
}
.menu-list .item-header,
.menu-list .item-footer,
.menu-list .item-title,
.menu-list .item-after,
.menu-list .item-text,
.menu-list .item-subtitle {
  transition-duration: 300ms;
}
.menu-list li:not(.list-group-title) {
  padding-top: 4px;
  padding-bottom: 4px;
}
.menu-list li:not(.list-group-title):first-child {
  padding-top: 4px;
}
.menu-list li:not(.list-group-title):last-child {
  padding-bottom: 4px;
}
.menu-list .item-link {
  margin-left: var(--f7-menu-list-offset);
  margin-right: var(--f7-menu-list-offset);
  border-radius: var(--f7-menu-list-border-radius) !important;
  color: var(--f7-menu-list-item-text-color);
  background-color: var(--f7-menu-list-item-bg-color);
}
.menu-list .item-inner:after {
  display: none;
}
.menu-list .item-selected .item-link,
.menu-list .item-selected.item-link,
.menu-list .tab-link-active .item-link,
.menu-list .tab-link-active.item-link {
  color: var(--f7-menu-list-item-selected-text-color);
  background-color: var(--f7-menu-list-item-selected-bg-color);
}
.menu-list .item-selected .item-header,
.menu-list .item-selected .item-footer,
.menu-list .item-selected .item-title,
.menu-list .item-selected .item-after,
.menu-list .item-selected .item-text,
.menu-list .item-selected .item-subtitle {
  color: var(--f7-menu-list-item-selected-text-color);
}
.ios .item-link.active-state .item-inner:after,
.ios .list-button.active-state:after,
.ios .links-list a.active-state:after {
  background-color: transparent;
}
.ios .links-list a.active-state,
.ios .list .item-link.active-state,
.ios .list .list-button.active-state {
  transition-duration: 0ms;
}
.md .menu-list .item-link.active-state {
  background-color: var(--f7-menu-list-item-bg-color);
}
/* === Badge === */
:root {
  --f7-badge-text-color: #fff;
  --f7-badge-bg-color: #8e8e93;
  --f7-badge-in-icon-size: 16px;
  --f7-badge-in-icon-font-size: 10px;
  --f7-badge-font-weight: normal;
  --f7-badge-font-size: 12px;
}
.ios {
  --f7-badge-padding: 0 4px;
  --f7-badge-size: 20px;
  --f7-badge-font-weight: 600;
}
.md {
  --f7-badge-padding: 2px 6px;
  --f7-badge-size: 20px;
  --f7-badge-font-weight: 500;
}
.badge {
  display: inline-flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  color: var(--f7-badge-text-color);
  background: var(--f7-badge-bg-color);
  position: relative;
  box-sizing: border-box;
  text-align: center;
  vertical-align: middle;
  font-weight: var(--f7-badge-font-weight);
  font-size: var(--f7-badge-font-size);
  border-radius: var(--f7-badge-size);
  padding: var(--f7-badge-padding);
  height: var(--f7-badge-size);
  min-width: var(--f7-badge-size);
}
.icon .badge,
.f7-icons .badge,
.framework7-icons .badge,
.material-icons .badge {
  position: absolute;
  left: 100%;
  margin-left: -10px;
  top: -2px;
  font-family: var(--f7-font-family);
  --f7-badge-font-size: var(--f7-badge-in-icon-font-size);
  --f7-badge-size: var(--f7-badge-in-icon-size);
  --f7-badge-padding: 0px 2px;
}
.badge[class*='color-'] {
  --f7-badge-bg-color: var(--f7-theme-color);
}
:root {
  --f7-button-font-size: 14px;
  --f7-button-min-width: 32px;
  --f7-button-bg-color: transparent;
  --f7-button-border-width: 0px;
  /*
  --f7-button-text-color: var(--f7-theme-color);
  --f7-button-pressed-text-color: var(--f7-button-text-color, var(--f7-theme-color));
  --f7-button-border-color: var(--f7-theme-color);
  --f7-button-fill-bg-color: var(--f7-theme-color);
  --f7-button-padding-vertical: 0px;
  */
  --f7-button-raised-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --f7-button-raised-pressed-box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16),
    0 3px 6px rgba(0, 0, 0, 0.23);
  --f7-segmented-raised-divider-color: rgba(0, 0, 0, 0.1);
  --f7-segmented-strong-padding: 2px;
  --f7-segmented-strong-between-buttons: 4px;
  --f7-segmented-strong-button-font-weight: 500;
  --f7-segmented-strong-button-active-box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
  --f7-segmented-strong-button-text-color: #000;
  --f7-segmented-strong-button-hover-bg-color: rgba(0, 0, 0, 0.04);
  --f7-segmented-strong-button-active-text-color: #000;
  --f7-segmented-strong-button-active-bg-color: #fff;
}
:root .dark,
:root.dark {
  --f7-segmented-strong-button-hover-bg-color: rgba(255, 255, 255, 0.02);
  --f7-segmented-strong-button-active-bg-color: rgba(255, 255, 255, 0.14);
  --f7-segmented-strong-button-text-color: #fff;
  --f7-segmented-strong-button-active-text-color: #fff;
}
.ios {
  --f7-button-fill-text-color: #fff;
  --f7-button-text-transform: uppercase;
  --f7-button-height: 28px;
  --f7-button-padding-horizontal: 10px;
  --f7-button-border-radius: 4px;
  --f7-button-font-weight: 600;
  --f7-button-letter-spacing: 0;
  /*
  --f7-button-outline-border-color: var(--f7-theme-color);
  */
  --f7-button-outline-border-width: 2px;
  /*
  --f7-button-pressed-bg-color: rgba(var(--f7-theme-color-rgb), .15);
  --f7-button-fill-pressed-bg-color: var(--f7-theme-color-tint);
  */
  --f7-button-large-text-transform: uppercase;
  --f7-button-large-height: 44px;
  --f7-button-large-font-size: 17px;
  --f7-button-large-font-weight: 500;
  --f7-button-small-outline-border-width: 2px;
  --f7-button-small-text-transform: uppercase;
  --f7-button-small-height: 26px;
  --f7-button-small-font-size: 13px;
  --f7-button-small-font-weight: 600;
  --f7-segmented-strong-button-text-transform: none;
  --f7-segmented-strong-button-active-font-weight: 600;
  --f7-button-tonal-bg-color: rgba(var(--f7-theme-color-rgb), 0.15);
  --f7-button-tonal-pressed-bg-color: rgba(var(--f7-theme-color-rgb), 0.25);
  --f7-button-tonal-text-color: var(--f7-theme-color);
  --f7-segmented-strong-bg-color: rgba(0, 0, 0, 0.07);
  --f7-segmented-strong-button-pressed-bg-color: rgba(0, 0, 0, 0.07);
}
.ios .dark,
.ios.dark {
  --f7-segmented-strong-bg-color: rgba(255, 255, 255, 0.1);
  --f7-segmented-strong-button-pressed-bg-color: rgba(255, 255, 255, 0.04);
}
.md {
  /*
  --f7-button-pressed-bg-color: transparent;
  */
  --f7-button-text-transform: none;
  --f7-button-height: 40px;
  --f7-button-padding-horizontal: 16px;
  --f7-button-border-radius: 8px;
  --f7-button-font-weight: 500;
  --f7-button-letter-spacing: normal;
  --f7-button-outline-border-width: 1px;
  --f7-button-large-text-transform: none;
  --f7-button-large-height: 48px;
  --f7-button-large-font-size: 14px;
  --f7-button-large-font-weight: 500;
  --f7-button-small-text-transform: none;
  --f7-button-small-outline-border-width: 1px;
  --f7-button-small-height: 32px;
  --f7-button-small-font-size: 14px;
  --f7-button-small-font-weight: 500;
  --f7-segmented-strong-button-text-transform: none;
  --f7-segmented-strong-button-active-font-weight: 500;
  --f7-segmented-strong-button-pressed-bg-color: transparent;
}
.md,
.md .dark,
.md [class*='color-'] {
  --f7-button-fill-pressed-bg-color: transparent;
  --f7-button-fill-text-color: var(--f7-md-on-primary);
  --f7-button-outline-border-color: var(--f7-md-outline);
  --f7-segmented-strong-bg-color: var(--f7-md-surface-variant);
  --f7-button-tonal-bg-color: var(--f7-md-secondary-container);
  --f7-button-tonal-pressed-bg-color: transparent;
  --f7-button-tonal-text-color: var(--f7-md-on-secondary-container);
}
button {
  -webkit-appearance: none;
          appearance: none;
  width: 100%;
}
.button {
  --f7-touch-ripple-color: rgba(var(--f7-theme-color-rgb), 0.25);
  text-decoration: none;
  text-align: center;
  display: flex;
  -webkit-appearance: none;
          appearance: none;
  background: none;
  margin: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  position: relative;
  overflow: hidden;
  font-family: inherit;
  cursor: pointer;
  outline: 0;
  box-sizing: border-box;
  vertical-align: middle;
  justify-content: center;
  align-items: center;
  border: var(--f7-button-border-width, 0px) solid var(--f7-button-border-color, var(--f7-theme-color));
  font-size: var(--f7-button-font-size);
  color: var(--f7-button-text-color, var(--f7-theme-color));
  height: var(--f7-button-height);
  line-height: calc(var(--f7-button-height) - var(--f7-button-border-width, 0) * 2);
  padding: var(--f7-button-padding-vertical, 0px) var(--f7-button-padding-horizontal);
  border-radius: var(--f7-button-border-radius);
  min-width: var(--f7-button-min-width);
  font-weight: var(--f7-button-font-weight);
  letter-spacing: var(--f7-button-letter-spacing);
  text-transform: var(--f7-button-text-transform);
  background-color: var(--f7-button-bg-color);
  box-shadow: var(--f7-button-box-shadow);
}
.button.active-state {
  color: var(--f7-button-pressed-text-color, var(--f7-button-text-color, var(--f7-theme-color)));
}
input[type='submit'].button,
input[type='button'].button,
input[type='reset'].button {
  width: 100%;
}
.button > i + span,
.button > span + span,
.button > span ~ i {
  margin-left: 4px;
}
.subnavbar .button,
.navbar .button,
.toolbar .button,
.searchbar .button {
  color: var(--f7-button-text-color, var(--f7-theme-color));
}
.button-round,
.ios .button-round-ios,
.md .button-round-md {
  --f7-button-border-radius: var(--f7-button-height);
}
.button-fill,
.ios .button-fill-ios,
.md .button-fill-md,
.button-active,
.button.tab-link-active {
  --f7-button-bg-color: var(--f7-button-fill-bg-color, var(--f7-theme-color));
  --f7-button-text-color: var(--f7-button-fill-text-color, #fff);
  --f7-touch-ripple-color: var(--f7-touch-ripple-white);
}
.ios .color-white .button-fill,
.ios .color-white .ios .button-fill-ios,
.ios .color-white .md .button-fill-md,
.ios .color-white .button-active,
.ios .color-white .button.tab-link-active,
.ios .button-fill.color-white,
.ios .ios .button-fill-ios.color-white,
.ios .md .button-fill-md.color-white,
.ios .button-active.color-white,
.ios .button.tab-link-active.color-white {
  --f7-button-text-color: #000;
}
.button-fill,
.ios .button-fill-ios,
.md .button-fill-md {
  --f7-button-pressed-bg-color: var(--f7-button-fill-pressed-bg-color);
}
.button-active,
.button.tab-link-active {
  --f7-button-pressed-bg-color: var(--f7-button-bg-color);
}
.button-outline,
.ios .button-outline-ios,
.md .button-outline-md {
  --f7-button-border-color: var(--f7-button-outline-border-color, var(--f7-theme-color));
  --f7-button-border-width: var(--f7-button-outline-border-width);
}
.button-tonal,
.ios .button-tonal-ios,
.md .button-tonal-md {
  --f7-button-bg-color: var(--f7-button-tonal-bg-color);
  --f7-button-text-color: var(--f7-button-tonal-text-color);
  --f7-button-pressed-bg-color: var(--f7-button-tonal-pressed-bg-color);
}
.button-large,
.ios .button-large-ios,
.md .button-large-md {
  --f7-button-height: var(--f7-button-large-height);
  --f7-button-font-size: var(--f7-button-large-font-size);
  --f7-button-font-weight: var(--f7-button-large-font-weight);
  --f7-button-text-transform: var(--f7-button-large-text-transform);
}
.button-small,
.ios .button-small-ios,
.md .button-small-md {
  --f7-button-outline-border-width: var(--f7-button-small-outline-border-width);
  --f7-button-height: var(--f7-button-small-height);
  --f7-button-font-size: var(--f7-button-small-font-size);
  --f7-button-font-weight: var(--f7-button-small-font-weight);
  --f7-button-text-transform: var(--f7-button-small-text-transform);
}
.ios .button-small.button-fill,
.ios .button-small-ios.button-fill,
.ios .button-small.button-fill-ios {
  --f7-button-border-width: var(--f7-button-small-outline-border-width);
  --f7-button-pressed-text-color: var(--f7-theme-color);
  --f7-button-pressed-bg-color: transparent;
}
.segmented {
  align-self: center;
  display: flex;
  flex-wrap: nowrap;
  border-radius: var(--f7-button-border-radius);
  box-shadow: var(--f7-button-box-shadow);
  box-sizing: border-box;
}
.segmented .button,
.segmented button {
  width: 100%;
  flex-shrink: 1;
  min-width: 0;
  border-radius: 0;
}
.segmented .button:first-child {
  border-radius: var(--f7-button-border-radius) 0 0 var(--f7-button-border-radius);
}
.segmented .button:not(.button-outline):first-child {
  border-left: none;
}
.segmented .button.button-outline:nth-child(n + 2) {
  border-left: none;
}
.segmented .button:last-child {
  border-radius: 0 var(--f7-button-border-radius) var(--f7-button-border-radius) 0;
}
.segmented .button-round:first-child {
  border-radius: var(--f7-button-height) 0 0 var(--f7-button-height);
}
.segmented .button-round:last-child {
  border-radius: 0 var(--f7-button-height) var(--f7-button-height) 0;
}
.segmented .button:first-child:last-child {
  border-radius: var(--f7-button-border-radius);
}
.segmented-raised,
.ios .segmented-raised-ios,
.md .segmented-raised-md {
  box-shadow: var(--f7-button-raised-box-shadow);
}
.segmented-raised .button:not(.button-outline),
.ios .segmented-raised-ios .button:not(.button-outline),
.md .segmented-raised-md .button:not(.button-outline) {
  border-left: 1px solid var(--f7-segmented-raised-divider-color);
}
.button-raised,
.ios .button-raised-ios,
.md .button-raised-md {
  --f7-button-box-shadow: var(--f7-button-raised-box-shadow);
}
.button-raised.active-state,
.ios .button-raised-ios.active-state,
.md .button-raised-md.active-state {
  --f7-button-box-shadow: var(--f7-button-raised-pressed-box-shadow);
}
.segmented-strong,
.ios .segmented-strong-ios,
.md .segmented-strong-md {
  --f7-button-bg-color: transparent;
  --f7-button-hover-bg-color: var(--f7-segmented-strong-button-hover-bg-color);
  --f7-button-text-color: var(--f7-segmented-strong-button-text-color);
  --f7-button-font-weight: var(--f7-segmented-strong-button-font-weight);
  --f7-button-text-transform: var(--f7-segmented-strong-button-text-transform);
  --f7-button-pressed-bg-color: var(--f7-segmented-strong-button-pressed-bg-color);
  position: relative;
  background: var(--f7-segmented-strong-bg-color);
  border-radius: calc(var(--f7-button-border-radius) + 2px);
  box-shadow: none;
  padding: var(--f7-segmented-strong-padding);
  overflow: hidden;
}
.segmented-strong .button,
.ios .segmented-strong-ios .button,
.md .segmented-strong-md .button {
  z-index: 1;
  transform: translate3d(0, 0, 0);
}
.segmented-strong .button,
.ios .segmented-strong-ios .button,
.md .segmented-strong-md .button,
.segmented-strong .button:first-child,
.ios .segmented-strong-ios .button:first-child,
.md .segmented-strong-md .button:first-child,
.segmented-strong .button:last-child,
.ios .segmented-strong-ios .button:last-child,
.md .segmented-strong-md .button:last-child {
  border-radius: var(--f7-button-border-radius);
}
.segmented-strong .button + .button,
.ios .segmented-strong-ios .button + .button,
.md .segmented-strong-md .button + .button {
  margin-left: var(--f7-segmented-strong-between-buttons);
}
.segmented-strong .button.button-active,
.ios .segmented-strong-ios .button.button-active,
.md .segmented-strong-md .button.button-active,
.segmented-strong .button.tab-link-active,
.ios .segmented-strong-ios .button.tab-link-active,
.md .segmented-strong-md .button.tab-link-active {
  --f7-button-hover-bg-color: transparent;
  --f7-button-bg-color: transparent;
  --f7-button-text-color: var(--f7-segmented-strong-button-active-text-color);
  --f7-button-font-weight: var(--f7-segmented-strong-button-active-font-weight);
}
.segmented-strong .segmented-highlight,
.ios .segmented-strong-ios .segmented-highlight,
.md .segmented-strong-md .segmented-highlight {
  --f7-segmented-highlight-num: 1;
  --f7-segmented-highlight-active: 0;
  --f7-segmented-highlight-padding: calc(var(--f7-segmented-strong-padding) * 2);
  --f7-segmented-highlight-between: var(--f7-segmented-strong-between-buttons);
  --f7-segmented-highlight-width: calc((100% - var(--f7-segmented-highlight-padding) - var(--f7-segmented-highlight-between) * (var(--f7-segmented-highlight-num) - 1)) / var(--f7-segmented-highlight-num));
  position: absolute;
  background-color: var(--f7-segmented-strong-button-active-bg-color);
  border-radius: var(--f7-button-border-radius);
  box-shadow: var(--f7-segmented-strong-button-active-box-shadow);
  left: var(--f7-segmented-strong-padding);
  top: var(--f7-segmented-strong-padding);
  height: calc(100% - var(--f7-segmented-strong-padding) * 2);
  width: var(--f7-segmented-highlight-width);
  z-index: 0;
  transform: translateX(calc(var(--f7-segmented-highlight-active) * 100% + var(--f7-segmented-highlight-active) * var(--f7-segmented-highlight-between)));
  transition: 200ms;
}
.button:nth-child(2) ~ .segmented-highlight {
  --f7-segmented-highlight-num: 2;
}
.button:nth-child(3) ~ .segmented-highlight {
  --f7-segmented-highlight-num: 3;
}
.button:nth-child(4) ~ .segmented-highlight {
  --f7-segmented-highlight-num: 4;
}
.button:nth-child(5) ~ .segmented-highlight {
  --f7-segmented-highlight-num: 5;
}
.button:nth-child(6) ~ .segmented-highlight {
  --f7-segmented-highlight-num: 6;
}
.button:nth-child(7) ~ .segmented-highlight {
  --f7-segmented-highlight-num: 7;
}
.button:nth-child(8) ~ .segmented-highlight {
  --f7-segmented-highlight-num: 8;
}
.button:nth-child(9) ~ .segmented-highlight {
  --f7-segmented-highlight-num: 9;
}
.button:nth-child(10) ~ .segmented-highlight {
  --f7-segmented-highlight-num: 10;
}
.button-active:nth-child(2) ~ .segmented-highlight,
.tab-link-active:nth-child(2) ~ .segmented-highlight {
  --f7-segmented-highlight-active: 1;
}
.button-active:nth-child(3) ~ .segmented-highlight,
.tab-link-active:nth-child(3) ~ .segmented-highlight {
  --f7-segmented-highlight-active: 2;
}
.button-active:nth-child(4) ~ .segmented-highlight,
.tab-link-active:nth-child(4) ~ .segmented-highlight {
  --f7-segmented-highlight-active: 3;
}
.button-active:nth-child(5) ~ .segmented-highlight,
.tab-link-active:nth-child(5) ~ .segmented-highlight {
  --f7-segmented-highlight-active: 4;
}
.button-active:nth-child(6) ~ .segmented-highlight,
.tab-link-active:nth-child(6) ~ .segmented-highlight {
  --f7-segmented-highlight-active: 5;
}
.button-active:nth-child(7) ~ .segmented-highlight,
.tab-link-active:nth-child(7) ~ .segmented-highlight {
  --f7-segmented-highlight-active: 6;
}
.button-active:nth-child(8) ~ .segmented-highlight,
.tab-link-active:nth-child(8) ~ .segmented-highlight {
  --f7-segmented-highlight-active: 7;
}
.button-active:nth-child(9) ~ .segmented-highlight,
.tab-link-active:nth-child(9) ~ .segmented-highlight {
  --f7-segmented-highlight-active: 8;
}
.button-active:nth-child(10) ~ .segmented-highlight,
.tab-link-active:nth-child(10) ~ .segmented-highlight {
  --f7-segmented-highlight-active: 9;
}
.segmented-round,
.ios .segmented-round-ios,
.md .segmented-round-md {
  border-radius: var(--f7-button-height);
}
.segmented-round .segmented-highlight,
.ios .segmented-round-ios .segmented-highlight,
.md .segmented-round-md .segmented-highlight {
  border-radius: var(--f7-button-height);
}
.subnavbar .segmented {
  width: 100%;
}
.button-preloader {
  position: relative;
  --f7-preloader-color: var(--f7-button-text-color, var(--f7-theme-color));
}
.button-preloader:not(.button-large) {
  --f7-preloader-size: calc(var(--f7-button-height) - 6px);
}
.button-preloader.button-fill {
  --f7-preloader-color: #fff;
}
.button-preloader > span:not(.preloader) {
  transition: 0.2s;
  display: inline-flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  transform: scale(1);
}
.button-preloader > .preloader {
  position: absolute;
  left: 50%;
  top: 50%;
  transition: 0.2s;
  transition-property: opacity;
  opacity: 0;
  visibility: hidden;
  margin-left: calc(-1 * var(--f7-preloader-size) / 2);
  margin-top: calc(-1 * var(--f7-preloader-size) / 2);
}
.button-preloader.button-loading > span:not(.preloader) {
  opacity: 0;
  transform: scale(0);
}
.button-preloader.button-loading > .preloader {
  opacity: 1;
  visibility: visible;
}
.ios .button {
  transition-duration: 100ms;
}
.ios .button.active-state {
  background-color: var(--f7-button-pressed-bg-color, rgba(var(--f7-theme-color-rgb), 0.15));
}
.ios .button-fill,
.ios .button-fill-ios {
  --f7-button-pressed-bg-color: var(--f7-button-fill-pressed-bg-color, var(--f7-theme-color-tint));
}
.ios .button-small,
.ios .button-small-ios {
  transition-duration: 200ms;
}
.md .button {
  transition-duration: 300ms;
  transform: translate3d(0, 0, 0);
}
.md .button-fill,
.md .button-fill-md {
  --f7-button-pressed-bg-color: var(--f7-button-fill-pressed-bg-color, transparent);
}
/* === Touch Ripple === */
:root {
  --f7-touch-ripple-black: rgba(0, 0, 0, 0.1);
  --f7-touch-ripple-white: rgba(255, 255, 255, 0.15);
  --f7-touch-ripple-color: var(--f7-touch-ripple-black);
}
.dark {
  --f7-touch-ripple-color: var(--f7-touch-ripple-white);
}
.ripple,
.fab a,
.link,
.item-link,
.list-button,
.button,
.dialog-button,
.tab-link,
.radio,
.checkbox,
.actions-button,
.speed-dial-buttons a {
  -webkit-user-select: none;
  user-select: none;
}
.ripple-wave {
  left: 0;
  top: 0;
  position: absolute !important;
  border-radius: 50%;
  pointer-events: none;
  z-index: -1;
  padding: 0;
  margin: 0;
  font-size: 0;
  transform: translate3d(0px, 0px, 0) scale(0);
  background-color: var(--f7-touch-ripple-color);
  animation: touch-ripple-in 200ms forwards;
}
.ripple-wave.ripple-wave-inset {
  background-image: none;
  background-color: var(--f7-touch-ripple-color);
}
.ripple-wave.ripple-wave-out {
  transform: var(--f7-ripple-transform);
  animation: touch-ripple-out 250ms forwards !important;
}
.item-link .ripple-wave,
.list label.item-content .ripple-wave,
.list-button .ripple-wave,
.links-list a .ripple-wave,
.button .ripple-wave,
button .ripple-wave,
.dialog-button .ripple-wave,
.tab-link .ripple-wave,
.item-radio .ripple-wave,
.item-checkbox .ripple-wave,
.actions-button .ripple-wave,
.fab a .ripple-wave,
.stepper-button .ripple-wave,
.stepper-button-minus .ripple-wave,
.stepper-button-plus .ripple-wave,
.list.accordion-list .accordion-item-toggle .ripple-wave,
.photo-browser .toolbar .link .ripple-wave,
.ripple-blur .ripple-wave {
  animation: touch-ripple-in-blurred 250ms forwards;
  background-image: radial-gradient(closest-side circle at 50% 50%, var(--f7-touch-ripple-color) 50%, rgba(255, 255, 255, 0));
  background-color: transparent;
}
.checkbox .ripple-wave,
.radio .ripple-wave,
.data-table .sortable-cell .ripple-wave {
  z-index: 0;
}
[class*='ripple-color-'] {
  --f7-touch-ripple-color: var(--f7-theme-color-ripple-color);
}
@keyframes touch-ripple-in-blurred {
  from {
    transform: translate3d(0px, 0px, 0) scale(0.5);
  }
  to {
    transform: var(--f7-ripple-transform);
  }
}
@keyframes touch-ripple-in {
  from {
    transform: translate3d(0px, 0px, 0) scale(0);
  }
  to {
    transform: var(--f7-ripple-transform);
  }
}
@keyframes touch-ripple-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
/* === Icon === */
.f7-icons,
.material-icons {
  width: 1em;
  height: 1em;
}
i.icon {
  display: inline-block;
  vertical-align: middle;
  background-size: 100% auto;
  background-position: center;
  background-repeat: no-repeat;
  font-style: normal;
  position: relative;
}
.icon-back:after,
.icon-prev:after,
.icon-forward:after,
.icon-next:after {
  font-family: 'framework7-core-icons';
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'liga';
  text-align: center;
  display: block;
  width: 100%;
  height: 100%;
  font-size: 20px;
}
.icon[class*='color-'] {
  color: var(--f7-theme-color);
}
.ios .icon-back,
.ios .icon-prev,
.ios .icon-forward,
.ios .icon-next {
  width: 12px;
  height: 20px;
  line-height: 20px;
}
.ios .icon-back:after,
.ios .icon-prev:after,
.ios .icon-forward:after,
.ios .icon-next:after {
  line-height: inherit;
}
.ios .icon-prev:after,
.ios .icon-next:after {
  font-size: 16px;
}
.ios .icon-back:after,
.ios .icon-prev:after {
  content: 'chevron_left_ios';
}
.ios .icon-forward:after,
.ios .icon-next:after {
  content: 'chevron_right_ios';
}
.md .icon-back,
.md .icon-forward,
.md .icon-next,
.md .icon-prev {
  width: 24px;
  height: 24px;
}
.md .icon-back:after,
.md .icon-forward:after,
.md .icon-next:after,
.md .icon-prev:after {
  line-height: 1.2;
}
.md .icon-back:after {
  content: 'arrow_left_md';
}
.md .icon-forward:after {
  content: 'arrow_right_md';
}
.md .icon-next:after {
  content: 'chevron_right_md';
}
.md .icon-prev:after {
  content: 'chevron_left_md';
}
.custom-modal-backdrop {
  z-index: 10500;
}
.custom-modal-backdrop,
.actions-backdrop,
.dialog-backdrop,
.popover-backdrop,
.popup-backdrop,
.preloader-backdrop,
.sheet-backdrop,
.fab-backdrop {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 13000;
  visibility: hidden;
  opacity: 0;
  transition-duration: 400ms;
}
.custom-modal-backdrop.not-animated,
.actions-backdrop.not-animated,
.dialog-backdrop.not-animated,
.popover-backdrop.not-animated,
.popup-backdrop.not-animated,
.preloader-backdrop.not-animated,
.sheet-backdrop.not-animated,
.fab-backdrop.not-animated {
  transition-duration: 0ms;
}
.custom-modal-backdrop.backdrop-in,
.actions-backdrop.backdrop-in,
.dialog-backdrop.backdrop-in,
.popover-backdrop.backdrop-in,
.popup-backdrop.backdrop-in,
.preloader-backdrop.backdrop-in,
.sheet-backdrop.backdrop-in,
.fab-backdrop.backdrop-in {
  visibility: visible;
  opacity: 1;
}
