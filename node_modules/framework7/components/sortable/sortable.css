.ios{--f7-sortable-handler-width:36px;--f7-sortable-sorting-item-box-shadow:0px 2px 8px rgba(0, 0, 0, 0.6);--f7-sortable-handler-color:rgba(0, 0, 0, 0.22);--f7-sortable-sorting-item-bg-color:rgba(255, 255, 255, 0.8)}.ios .dark,.ios.dark{--f7-sortable-sorting-item-bg-color:rgba(50, 50, 50, 0.8);--f7-sortable-handler-color:rgba(255, 255, 255, 0.55)}.md{--f7-sortable-handler-width:42px;--f7-sortable-sorting-item-box-shadow:none}.md,.md .dark,.md [class*=color-]{--f7-sortable-handler-color:var(--f7-md-on-surface-variant);--f7-sortable-sorting-item-bg-color:var(--f7-md-surface-3)}.sortable .sortable-handler{width:var(--f7-sortable-handler-width);height:100%;position:absolute;top:0;z-index:10;opacity:0;pointer-events:none;cursor:move;transition-duration:.3s;display:flex;align-items:center;justify-content:center;overflow:hidden;right:var(--f7-safe-area-right)}.sortable .sortable-handler:after{font-family:framework7-core-icons;font-weight:400;font-style:normal;line-height:1;letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale;font-feature-settings:'liga';text-align:center;display:block;width:100%;height:100%;font-size:20px;transition-duration:.3s;transform:translateX(10px);color:var(--f7-sortable-handler-color);overflow:hidden;height:20px;width:18px}.sortable li.sorting{z-index:50;background:var(--f7-sortable-sorting-item-bg-color);transition-duration:0s;box-shadow:var(--f7-sortable-sorting-item-box-shadow)}.sortable li.sorting .item-inner:after{display:none!important}.sortable-opposite .sortable-handler{left:var(--f7-safe-area-right);right:auto}.sortable-opposite .sortable-handler:after{transform:translateX(-10px)}.sortable-opposite .item-content,.sortable-opposite.links-list li a,.sortable.simple-list li,.sortable:not(.sortable-opposite) .item-inner{transition-duration:.3s;transition-property:all}.sortable-sorting li{transition-duration:.3s}.sortable-enabled li:not(.no-sorting):not(.disallow-sorting) .sortable-handler{pointer-events:auto;touch-action:none;opacity:1}.sortable-enabled li:not(.no-sorting):not(.disallow-sorting) .sortable-handler:after{transform:translateX(0px)}.sortable-enabled:not(.sortable-opposite) li:not(.no-sorting):not(.disallow-sorting) .item-link .item-inner:before,.sortable-enabled:not(.sortable-opposite) li:not(.no-sorting):not(.disallow-sorting) .item-link .item-title-row:before,.sortable-enabled:not(.sortable-opposite).links-list li:not(.no-sorting):not(.disallow-sorting)>a:before{display:none}.links-list.sortable-enabled:not(.sortable-opposite) li:not(.no-sorting):not(.disallow-sorting) a,.no-chevron .sortable-enabled:not(.sortable-opposite) li:not(.no-sorting):not(.disallow-sorting) .item-link .item-inner,.simple-list.sortable-enabled:not(.sortable-opposite) li:not(.no-sorting):not(.disallow-sorting),.sortable-enabled :not(.sortable-opposite).no-chevron .item-link .item-inner,.sortable-enabled.no-chevron:not(.sortable-opposite) li:not(.no-sorting):not(.disallow-sorting) .item-link .item-inner,.sortable-enabled:not(.sortable-opposite) li:not(.no-sorting):not(.disallow-sorting) .item-inner,.sortable-enabled:not(.sortable-opposite) li:not(.no-sorting):not(.disallow-sorting) .item-link .item-inner,.sortable-enabled:not(.sortable-opposite) li:not(.no-sorting):not(.disallow-sorting) .item-link.no-chevron .item-inner{padding-right:calc(var(--f7-sortable-handler-width) + var(--f7-safe-area-right))}.links-list.sortable-opposite.sortable-enabled li:not(.no-sorting):not(.disallow-sorting) a,.simple-list.sortable-opposite.sortable-enabled li:not(.no-sorting):not(.disallow-sorting),.sortable-opposite.sortable-enabled li:not(.no-sorting):not(.disallow-sorting) .item-content{padding-left:calc(var(--f7-sortable-handler-width) + var(--f7-safe-area-right))}.ios .sortable-handler:after{content:'sort_ios'}.md .sortable-handler:after{content:'sort_md'}