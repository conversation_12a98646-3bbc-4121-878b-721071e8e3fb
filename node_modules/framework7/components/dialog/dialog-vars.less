:root {
  --f7-dialog-button-text-color: var(--f7-theme-color);
  --f7-dialog-button-text-align: center;
}
.ios-vars({

  --f7-dialog-width: 270px;
  --f7-dialog-inner-padding: 16px;
  --f7-dialog-border-radius: 13px;
  --f7-dialog-text-align: center;
  --f7-dialog-font-size: 14px;
  --f7-dialog-title-text-color: inherit;
  --f7-dialog-title-font-size: 18px;
  --f7-dialog-title-font-weight: 600;
  --f7-dialog-title-line-height: inherit;

  --f7-dialog-button-font-size: 17px;
  --f7-dialog-button-height: 44px;
  --f7-dialog-button-letter-spacing: 0;
  --f7-dialog-button-font-weight: 400;
  --f7-dialog-button-text-transform: none;
  --f7-dialog-button-strong-bg-color: transparent;
  --f7-dialog-button-strong-text-color: var(--f7-theme-color);
  --f7-dialog-button-strong-font-weight: 500;

  --f7-dialog-input-border-radius: 4px;
  --f7-dialog-input-font-size: 14px;
  --f7-dialog-input-height: 32px;
  --f7-dialog-input-border-width: 1px;
  --f7-dialog-input-placeholder-color: #a9a9a9;

  --f7-dialog-preloader-size: 34px;

  .light-vars({
    --f7-dialog-input-bg-color:  #fff;
    --f7-dialog-bg-color: rgba(255,255,255,0.95);
    --f7-dialog-bg-color-rgb: 255, 255, 255;
    --f7-dialog-text-color: #000;
    --f7-dialog-button-pressed-bg-color: rgba(0,0,0,0.1);
    --f7-dialog-button-strong-pressed-bg-color: rgba(0,0,0,0.1);
    --f7-dialog-input-border-color: rgba(0,0,0,0.3);
    --f7-dialog-border-divider-color: rgba(0,0,0,0.2);
  });
  .dark-vars({
    --f7-dialog-text-color: #fff;
    --f7-dialog-bg-color: rgba(45,45,45,0.95);
    --f7-dialog-bg-color-rgb: 45, 45, 45;
    --f7-dialog-button-pressed-bg-color: rgba(0,0,0,0.2);
    --f7-dialog-button-strong-pressed-bg-color: rgba(0,0,0,0.2);
    --f7-dialog-border-divider-color: rgba(255,255,255,0.15);
    --f7-dialog-input-border-color: rgba(255,255,255,0.15);
    --f7-dialog-input-bg-color: rgba(0,0,0,0.5);
  });
});
.md-vars({
  --f7-dialog-width: 280px;
  --f7-dialog-inner-padding: 24px;
  --f7-dialog-border-radius: 28px;
  .ltr({
    --f7-dialog-text-align: left;
  });
  .rtl({
    --f7-dialog-text-align: right;
  });
  --f7-dialog-font-size: 14px;
  --f7-dialog-title-font-size: 24px;
  --f7-dialog-title-font-weight: 400;
  --f7-dialog-title-line-height: 1.3;
  --f7-dialog-button-font-size: 14px;
  --f7-dialog-button-height: 40px;
  --f7-dialog-button-letter-spacing: normal;
  --f7-dialog-button-font-weight: 500;
  --f7-dialog-button-text-transform: none;
  --f7-dialog-button-strong-font-weight: 500;
  --f7-dialog-input-border-radius: 0px;
  --f7-dialog-input-font-size: 16px;
  --f7-dialog-input-height: 36px;
  --f7-dialog-input-border-color: transparent;
  --f7-dialog-input-border-width: 0px;
  --f7-dialog-preloader-size: 32px;
  --f7-dialog-input-bg-color: transparent;
});

.md-color-vars({
  --f7-dialog-button-pressed-bg-color: transparent;
  --f7-dialog-button-strong-bg-color: var(--f7-theme-color);
  --f7-dialog-button-strong-text-color: var(--f7-md-on-primary);
  --f7-dialog-button-strong-pressed-bg-color: transparent;
  --f7-dialog-bg-color: var(--f7-md-surface-3);
  --f7-dialog-input-placeholder-color: var(--f7-md-on-surface-variant);
  --f7-dialog-text-color: var(--f7-md-on-surface-variant);
  --f7-dialog-title-text-color: var(--f7-md-on-surface);
});
