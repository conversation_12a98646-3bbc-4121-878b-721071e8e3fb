:root {
  --f7-fab-margin: 16px;
  --f7-fab-extended-text-font-size: 14px;
  --f7-fab-label-padding: 4px 12px;
  --f7-fab-label-font-size: inherit;
  --f7-fab-button-size: 40px;
}
.ios-vars({
  /* --f7-fab-pressed-bg-color: var(--f7-theme-color-shade); */
  /* --f7-fab-bg-color: var(--f7-theme-color)); */
  --f7-fab-text-color: #fff;
  --f7-fab-border-radius: 50px;
  --f7-fab-size: 50px;
  --f7-fab-box-shadow: 0px 2px 4px rgba(0,0,0,0.4);
  --f7-fab-extended-text-transform: uppercase;
  --f7-fab-extended-size: 50px;
  --f7-fab-extended-text-padding: 0 20px;
  --f7-fab-extended-text-font-weight: 600;
  --f7-fab-extended-text-letter-spacing: 0;
  --f7-fab-label-border-radius: 4px;
  --f7-fab-label-box-shadow: 0px 1px 2px rgba(0,0,0,0.4);
  --f7-fab-label-text-color: #333;
  --f7-fab-label-bg-color: #fff;
});
.md-vars({
  /*
  --f7-fab-pressed-bg-color: var(--f7-fab-bg-color, var(--f7-theme-color));
  */

  --f7-fab-border-radius: 16px;
  --f7-fab-size: 56px;
  --f7-fab-box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
  --f7-fab-extended-size: 56px;
  --f7-fab-extended-text-padding: 0 16px;
  --f7-fab-extended-text-font-weight: 500;
  --f7-fab-extended-text-letter-spacing: 0;
  --f7-fab-extended-text-transform: none;
  --f7-fab-label-border-radius: 8px;
  --f7-fab-label-box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
});
.md-color-vars({
  --f7-fab-bg-color: var(--f7-md-primary-container);
  --f7-fab-text-color: var(--f7-md-on-primary-container);
  --f7-fab-label-text-color: var(--f7-md-on-surface);
  --f7-fab-label-bg-color: var(--f7-md-surface-5);
});
