:root {
  --f7-popover-width: 260px;
}
.ios-vars({
  --f7-popover-border-radius: 13px;
  --f7-popover-actions-icon-size: 28px;
  --f7-popover-transition-timing-function: initial;
  .light-vars({
    --f7-popover-bg-color: rgba(255,255,255,0.95);
    --f7-popover-actions-label-text-color: rgba(0,0,0,0.45);
  });
  .dark-vars({
    --f7-popover-bg-color: rgba(30,30,30,0.95);
    --f7-popover-actions-label-text-color: rgba(255,255,255,0.55);
  });
});
.md-vars({
  --f7-popover-transition-timing-function: cubic-bezier(0, 0.8, 0.34, 1);
  --f7-popover-border-radius: 28px;
  --f7-popover-actions-icon-size: 24px;
});
.md-color-vars({
  --f7-popover-bg-color: var(--f7-md-surface-3);
  --f7-popover-actions-label-text-color: var(--f7-md-on-surface-variant);
});
