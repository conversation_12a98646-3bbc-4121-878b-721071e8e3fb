.ios {
  .item-label,
  .item-floating-label {
    + .item-input-wrap {
      margin-top: 0;
    }
  }
  .item-floating-label {
    transform: scale(var(--f7-floating-label-scale)) translateY(calc(var(--f7-label-height) + 25%));
  }
  .item-input-focused .item-floating-label {
    color: var(--f7-label-text-color);
  }
  .item-input {
    .item-media {
      align-self: flex-start;
    }
  }
  .item-input-wrap {
    margin-top: calc(-1 * var(--f7-list-item-padding-vertical));
    margin-bottom: calc(-1 * var(--f7-list-item-padding-vertical));
  }

  .item-input:not(.item-input-outline) {
    .item-content,
    &.item-content {
      background: var(--f7-input-item-bg-color);
    }
  }

  .item-input-error-message,
  .item-input-info,
  .input-error-message,
  .input-info {
    position: relative;
    margin-bottom: 6px;
    margin-top: -8px;
  }

  .item-input-focused {
    .item-label,
    .item-floating-label {
      color: var(--f7-label-focused-text-color, var(--f7-label-text-color));
    }
    .item-inner:after {
      background: var(--f7-input-focused-border-color, var(--f7-list-item-border-color));
    }
  }
  .item-input-invalid {
    .item-label,
    .item-floating-label {
      color: var(--f7-label-invalid-text-color, var(--f7-label-text-color));
    }
    .item-inner:after {
      background: var(--f7-input-invalid-border-color, var(--f7-list-item-border-color));
    }
  }
  .item-input-invalid,
  .input-invalid {
    input,
    select,
    textarea {
      color: var(--f7-input-invalid-text-color, var(--f7-input-error-text-color));
    }
  }

  .input-clear-button {
    &:after {
      content: 'delete_round_ios';
      font-size: calc(var(--f7-input-clear-button-size) / (14 / 10));
      line-height: 1.4;
    }
    &:before {
      width: 44px;
      height: 44px;
      margin-left: -22px;
      margin-top: -22px;
    }
  }

  .item-input-outline .item-content,
  .item-input-outline.item-content {
    padding-top: 8px;
    padding-bottom: 8px;
    margin-left: 16px;
    margin-right: 16px;
  }
  li.item-input-outline:first-child,
  li:first-child > .item-input-outline {
    padding-top: 16px;
    &::after {
      top: 16px;
    }
  }
  li.item-input-outline:last-child,
  li:last-child > .item-input-outline {
    padding-bottom: 16px;
    &::after {
      bottom: 16px;
    }
  }

  .item-input-outline .item-content,
  .item-input-outline.item-content,
  .input-outline {
    .item-inner {
      display: block;
      padding-top: 0px;
      padding-bottom: 0px;
    }
    .item-input-wrap {
      margin-top: 2px;
    }
    .item-title + .item-input-wrap {
      margin-top: -22px;
    }

    .item-label,
    .item-floating-label {
      display: inline-flex;
      margin: -8px -4px -4px;
      top: -4px;
    }

    .item-floating-label {
      transform: scale(var(--f7-floating-label-scale)) translateY(calc(var(--f7-label-height)));
    }
  }
}
