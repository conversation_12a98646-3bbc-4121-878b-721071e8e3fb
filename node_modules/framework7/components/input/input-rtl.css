:root{--f7-input-bg-color:transparent;--f7-label-font-weight:400;--f7-label-height:16px;--f7-label-font-size:12px;--f7-floating-label-scale:calc(16 / 12);--f7-input-padding-left:0px;--f7-input-padding-right:0px;--f7-input-error-text-color:#ff3b30;--f7-input-error-font-size:12px;--f7-input-error-line-height:1.4;--f7-input-error-font-weight:400;--f7-input-info-font-size:12px;--f7-input-info-line-height:1.4;--f7-textarea-height:100px}.ios{--f7-input-item-bg-color:transparent;--f7-input-item-border-radius:0px;--f7-input-height:40px;--f7-input-font-size:16px;--f7-input-placeholder-color:#a9a9a9;--f7-textarea-padding-vertical:8px;--f7-label-text-color:inherit;--f7-input-clear-button-size:14px;--f7-input-outline-border-radius:8px;--f7-input-text-color:#000000;--f7-input-info-text-color:rgba(0, 0, 0, 0.45);--f7-input-clear-button-color:rgba(0, 0, 0, 0.45);--f7-input-outline-border-color:#bbb}.ios .dark,.ios.dark{--f7-input-text-color:#fff;--f7-input-info-text-color:rgba(255, 255, 255, 0.55);--f7-input-clear-button-color:rgba(255, 255, 255, 0.5);--f7-input-outline-border-color:#444}.md{--f7-input-item-border-radius:4px 4px 0 0;--f7-input-height:24px;--f7-input-font-size:16px;--f7-textarea-padding-vertical:0px;--f7-input-outline-border-radius:4px;--f7-floating-label-scale:calc(16 / 12);--f7-input-clear-button-size:24px;--f7-input-info-text-color:rgba(0, 0, 0, 0.45)}.md .dark,.md.dark{--f7-input-info-text-color:rgba(255, 255, 255, 0.45)}.md,.md .dark,.md [class*=color-]{--f7-input-placeholder-color:var(--f7-md-on-surface-variant);--f7-input-item-bg-color:var(--f7-md-surface-variant);--f7-input-border-color:var(--f7-md-outline);--f7-input-clear-button-color:var(--f7-md-on-surface-variant);--f7-input-outline-border-color:var(--f7-md-outline);--f7-input-text-color:var(--f7-md-on-surface);--f7-label-text-color:var(--f7-md-on-surface-variant)}input[type=date],input[type=datetime-local],input[type=email],input[type=month],input[type=number],input[type=password],input[type=search],input[type=tel],input[type=text],input[type=time],input[type=url],select,textarea{box-sizing:border-box;-webkit-appearance:none;appearance:none;border:none;box-shadow:none;border-radius:0;outline:0;display:block;padding:0;margin:0;font-family:inherit;background:0 0;resize:none;font-size:inherit;color:inherit}input[type=date]:-internal-autofill-selected,input[type=datetime-local]:-internal-autofill-selected,input[type=email]:-internal-autofill-selected,input[type=month]:-internal-autofill-selected,input[type=number]:-internal-autofill-selected,input[type=password]:-internal-autofill-selected,input[type=search]:-internal-autofill-selected,input[type=tel]:-internal-autofill-selected,input[type=text]:-internal-autofill-selected,input[type=time]:-internal-autofill-selected,input[type=url]:-internal-autofill-selected,select:-internal-autofill-selected,textarea:-internal-autofill-selected{background-color:transparent!important;transition:background-color 5000s ease-in-out 0s!important}input[type=date]:-webkit-autofill,input[type=datetime-local]:-webkit-autofill,input[type=email]:-webkit-autofill,input[type=month]:-webkit-autofill,input[type=number]:-webkit-autofill,input[type=password]:-webkit-autofill,input[type=search]:-webkit-autofill,input[type=tel]:-webkit-autofill,input[type=text]:-webkit-autofill,input[type=time]:-webkit-autofill,input[type=url]:-webkit-autofill,select:-webkit-autofill,textarea:-webkit-autofill{background-color:transparent!important;-webkit-transition:background-color 5000s ease-in-out 0s!important;transition:background-color 5000s ease-in-out 0s!important}input[type=date]:-webkit-autofill-and-obscured,input[type=date]:autofill,input[type=datetime-local]:-webkit-autofill-and-obscured,input[type=datetime-local]:autofill,input[type=email]:-webkit-autofill-and-obscured,input[type=email]:autofill,input[type=month]:-webkit-autofill-and-obscured,input[type=month]:autofill,input[type=number]:-webkit-autofill-and-obscured,input[type=number]:autofill,input[type=password]:-webkit-autofill-and-obscured,input[type=password]:autofill,input[type=search]:-webkit-autofill-and-obscured,input[type=search]:autofill,input[type=tel]:-webkit-autofill-and-obscured,input[type=tel]:autofill,input[type=text]:-webkit-autofill-and-obscured,input[type=text]:autofill,input[type=time]:-webkit-autofill-and-obscured,input[type=time]:autofill,input[type=url]:-webkit-autofill-and-obscured,input[type=url]:autofill,select:-webkit-autofill-and-obscured,select:autofill,textarea:-webkit-autofill-and-obscured,textarea:autofill{background-color:transparent!important;-webkit-transition:background-color 5000s ease-in-out 0s!important;transition:background-color 5000s ease-in-out 0s!important}.textarea-resizable-shadow{opacity:0;position:absolute;z-index:-1000;pointer-events:none;left:-1000px;top:-1000px;visibility:hidden}.list input[type=date],.list input[type=datetime-local],.list input[type=email],.list input[type=month],.list input[type=number],.list input[type=password],.list input[type=search],.list input[type=tel],.list input[type=text],.list input[type=time],.list input[type=url],.list select{width:100%;height:var(--f7-input-height);color:var(--f7-input-text-color);font-size:var(--f7-input-font-size);background-color:var(--f7-input-bg-color,transparent);padding-left:var(--f7-input-padding-left);padding-right:var(--f7-input-padding-right)}.list input[type=date]::placeholder,.list input[type=datetime-local]::placeholder,.list input[type=email]::placeholder,.list input[type=month]::placeholder,.list input[type=number]::placeholder,.list input[type=password]::placeholder,.list input[type=search]::placeholder,.list input[type=tel]::placeholder,.list input[type=text]::placeholder,.list input[type=time]::placeholder,.list input[type=url]::placeholder,.list select::placeholder{color:var(--f7-input-placeholder-color)}.list textarea{width:100%;color:var(--f7-input-text-color);font-size:var(--f7-input-font-size);resize:none;line-height:1.4;height:var(--f7-textarea-height);background-color:var(--f7-input-bg-color,transparent);padding-top:var(--f7-textarea-padding-vertical);padding-bottom:var(--f7-textarea-padding-vertical);padding-left:var(--f7-input-padding-left);padding-right:var(--f7-input-padding-right)}.list textarea::placeholder{color:var(--f7-input-placeholder-color)}.list textarea.resizable{height:calc(var(--f7-input-height) + var(--f7-textarea-padding-vertical) * 2)}.list input[type=date],.list input[type=datetime-local],.list input[type=month],.list input[type=time]{line-height:var(--f7-input-height)}.list input[type=date],.list input[type=datetime-local],.list input[type=month]{text-align:right;flex-direction:row-reverse;width:auto}.list .item-floating-label,.list .item-label{width:auto;vertical-align:top;flex-shrink:0;font-size:var(--f7-label-font-size);font-weight:var(--f7-label-font-weight);line-height:var(--f7-label-height);color:var(--f7-label-text-color);transition-duration:.2s;transition-property:transform,color}.list .item-floating-label{color:var(--f7-input-placeholder-color);max-width:calc(100% / var(--f7-floating-label-scale));pointer-events:none;right:var(--f7-input-padding-right);transform-origin:right center}.list .item-floating-label~.item-input-wrap input::placeholder,.list .item-floating-label~.item-input-wrap textarea::placeholder{opacity:0;transition-duration:.1s}.list .item-floating-label~.item-input-wrap input.input-focused::placeholder,.list .item-floating-label~.item-input-wrap textarea.input-focused::placeholder{opacity:1;transition-duration:.3s}.list .item-input-with-value .item-floating-label{color:var(--f7-label-text-color)}.list .item-input-focused .item-floating-label,.list .item-input-with-value .item-floating-label{transform:scale(1) translateY(0)!important}.list .item-input-wrap{width:100%;flex-shrink:1;position:relative}.input,.item-input{position:relative}.item-input .item-inner{display:flex;flex-direction:column;align-items:flex-start;justify-content:center}.input-error-message,.item-input-error-message{font-size:var(--f7-input-error-font-size);line-height:var(--f7-input-error-line-height);color:var(--f7-input-error-text-color);font-weight:var(--f7-input-error-font-weight);display:none;box-sizing:border-box}.input-info,.item-input-info{font-size:var(--f7-input-info-font-size);line-height:var(--f7-input-info-line-height);color:var(--f7-input-info-text-color)}.input-invalid .input-error-message,.input-invalid .item-input-error-message,.item-input-invalid .input-error-message,.item-input-invalid .item-input-error-message{display:block}.input-invalid .input-info,.input-invalid .item-input-info,.item-input-invalid .input-info,.item-input-invalid .item-input-info{display:none}.input{position:relative}.input input,.input select,.input textarea{width:100%}.input-clear-button{opacity:0;pointer-events:none;visibility:hidden;transition-duration:.1s;position:absolute;top:50%;border:none;padding:0;margin:0;outline:0;z-index:1;cursor:pointer;background:0 0;width:var(--f7-input-clear-button-size);height:var(--f7-input-clear-button-size);margin-top:calc(-1 * var(--f7-input-clear-button-size)/ 2);color:var(--f7-input-clear-button-color);left:0}.input-clear-button:after{font-family:framework7-core-icons;font-weight:400;font-style:normal;line-height:1;letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale;font-feature-settings:'liga';text-align:center;display:block;width:100%;height:100%;font-size:20px}.input-clear-button:before{position:absolute;content:'';left:50%;top:50%}.item-input-wrap .input-clear-button{top:calc(var(--f7-input-height)/ 2)}.input-clear-button.active-state{opacity:.75!important}.input-with-value .input-clear-button,.input-with-value~.input-clear-button,.item-input-with-value .input-clear-button{opacity:1;pointer-events:auto;visibility:visible}.input-dropdown,.input-dropdown-wrap{position:relative}.input-dropdown-wrap:before,.input-dropdown:before{content:'';pointer-events:none;position:absolute;top:50%;margin-top:-2px;width:0;height:0;border-left:4px solid transparent;border-right:4px solid transparent;border-top:5px solid #727272;left:6px}.input-dropdown input,.input-dropdown select,.input-dropdown textarea,.input-dropdown-wrap input,.input-dropdown-wrap select,.input-dropdown-wrap textarea{padding-left:calc(20px + var(--f7-input-padding-left))}.input-outline::after,.item-input-outline .item-content::after,.item-input-outline.item-content::after{content:'';position:absolute;left:0;right:0;top:8px;bottom:8px;border:1px solid var(--f7-input-outline-border-color);border-radius:var(--f7-input-outline-border-radius);pointer-events:none;box-sizing:border-box;transition-duration:.2s}.input-outline .item-floating-label,.input-outline .item-label,.item-input-outline .item-content .item-floating-label,.item-input-outline .item-content .item-label,.item-input-outline.item-content .item-floating-label,.item-input-outline.item-content .item-label{pointer-events:none;background:var(--f7-page-bg-color);z-index:1;padding:4px}.input-outline.input-focused::after,.input-outline.item-input-focused::after,.item-input-outline .item-content.input-focused::after,.item-input-outline .item-content.item-input-focused::after,.item-input-outline.item-content.input-focused::after,.item-input-outline.item-content.item-input-focused::after{border-width:2px;border-color:var(--f7-input-outline-invalid-border-color,var(--f7-theme-color))}.input-outline.input-invalid::after,.input-outline.item-input-invalid::after,.item-input-outline .item-content.input-invalid::after,.item-input-outline .item-content.item-input-invalid::after,.item-input-outline.item-content.input-invalid::after,.item-input-outline.item-content.item-input-invalid::after{border-width:2px;border-color:var(--f7-input-outline-invalid-border-color,var(--f7-input-error-text-color))}.block-strong .item-input-outline .item-floating-label,.block-strong .item-input-outline .item-label,.ios .block-strong-ios .item-input-outline .item-floating-label,.ios .block-strong-ios .item-input-outline .item-label,.md .block-strong-md .item-input-outline .item-floating-label,.md .block-strong-md .item-input-outline .item-label{background:var(--f7-block-strong-bg-color)!important}.ios .list-strong-ios .item-input-outline .item-floating-label,.ios .list-strong-ios .item-input-outline .item-label,.list-strong .item-input-outline .item-floating-label,.list-strong .item-input-outline .item-label,.md .list-strong-md .item-input-outline .item-floating-label,.md .list-strong-md .item-input-outline .item-label{background:var(--f7-list-strong-bg-color)!important}.dark option{background-color:var(--f7-page-bg-color)}.ios .item-floating-label+.item-input-wrap,.ios .item-label+.item-input-wrap{margin-top:0}.ios .item-floating-label{transform:scale(var(--f7-floating-label-scale)) translateY(calc(var(--f7-label-height) + 25%))}.ios .item-input-focused .item-floating-label{color:var(--f7-label-text-color)}.ios .item-input .item-media{align-self:flex-start}.ios .item-input-wrap{margin-top:calc(-1 * var(--f7-list-item-padding-vertical));margin-bottom:calc(-1 * var(--f7-list-item-padding-vertical))}.ios .item-input:not(.item-input-outline) .item-content,.ios .item-input:not(.item-input-outline).item-content{background:var(--f7-input-item-bg-color)}.ios .input-error-message,.ios .input-info,.ios .item-input-error-message,.ios .item-input-info{position:relative;margin-bottom:6px;margin-top:-8px}.ios .item-input-focused .item-floating-label,.ios .item-input-focused .item-label{color:var(--f7-label-focused-text-color,var(--f7-label-text-color))}.ios .item-input-focused .item-inner:after{background:var(--f7-input-focused-border-color,var(--f7-list-item-border-color))}.ios .item-input-invalid .item-floating-label,.ios .item-input-invalid .item-label{color:var(--f7-label-invalid-text-color,var(--f7-label-text-color))}.ios .item-input-invalid .item-inner:after{background:var(--f7-input-invalid-border-color,var(--f7-list-item-border-color))}.ios .input-invalid input,.ios .input-invalid select,.ios .input-invalid textarea,.ios .item-input-invalid input,.ios .item-input-invalid select,.ios .item-input-invalid textarea{color:var(--f7-input-invalid-text-color,var(--f7-input-error-text-color))}.ios .input-clear-button:after{content:'delete_round_ios';font-size:calc(var(--f7-input-clear-button-size) / (14 / 10));line-height:1.4}.ios .input-clear-button:before{width:44px;height:44px;margin-left:-22px;margin-top:-22px}.ios .item-input-outline .item-content,.ios .item-input-outline.item-content{padding-top:8px;padding-bottom:8px;margin-left:16px;margin-right:16px}.ios li.item-input-outline:first-child,.ios li:first-child>.item-input-outline{padding-top:16px}.ios li.item-input-outline:first-child::after,.ios li:first-child>.item-input-outline::after{top:16px}.ios li.item-input-outline:last-child,.ios li:last-child>.item-input-outline{padding-bottom:16px}.ios li.item-input-outline:last-child::after,.ios li:last-child>.item-input-outline::after{bottom:16px}.ios .input-outline .item-inner,.ios .item-input-outline .item-content .item-inner,.ios .item-input-outline.item-content .item-inner{display:block;padding-top:0px;padding-bottom:0px}.ios .input-outline .item-input-wrap,.ios .item-input-outline .item-content .item-input-wrap,.ios .item-input-outline.item-content .item-input-wrap{margin-top:2px}.ios .input-outline .item-title+.item-input-wrap,.ios .item-input-outline .item-content .item-title+.item-input-wrap,.ios .item-input-outline.item-content .item-title+.item-input-wrap{margin-top:-22px}.ios .input-outline .item-floating-label,.ios .input-outline .item-label,.ios .item-input-outline .item-content .item-floating-label,.ios .item-input-outline .item-content .item-label,.ios .item-input-outline.item-content .item-floating-label,.ios .item-input-outline.item-content .item-label{display:inline-flex;margin:-8px -4px -4px;top:-4px}.ios .input-outline .item-floating-label,.ios .item-input-outline .item-content .item-floating-label,.ios .item-input-outline.item-content .item-floating-label{transform:scale(var(--f7-floating-label-scale)) translateY(calc(var(--f7-label-height)))}.md .item-input-wrap{min-height:var(--f7-input-height)}.md .item-floating-label{transform:scale(var(--f7-floating-label-scale)) translateY(calc((var(--f7-input-height)/ 2)/ var(--f7-floating-label-scale)))}.md .item-input .item-inner:after{display:none!important}.md .item-input .item-content,.md .item-input.item-content{margin-left:16px;margin-right:16px;padding-top:8px;padding-bottom:8px}.md .input:after{content:'';position:absolute;background-color:var(--f7-input-border-color);display:block;z-index:15;top:auto;right:auto;bottom:0;left:0;height:1px;width:100%;transform-origin:50% 100%;transform:scaleY(calc(1 / var(--f7-device-pixel-ratio)))}.md .input::after{transform:scaleY(1)!important;transition-duration:.2s;bottom:0px}.md .input.input-focused::after{transform:scaleY(2)!important;background:var(--f7-input-focused-border-color,var(--f7-theme-color))}.md .input.input-invalid::after{transform:scaleY(2)!important;background:var(--f7-input-invalid-border-color,var(--f7-input-error-text-color))}.md .item-input:not(.item-input-outline) .item-content:after,.md .item-input:not(.item-input-outline).item-content:after{content:'';position:absolute;background-color:var(--f7-input-border-color);display:block;z-index:15;top:auto;right:auto;bottom:0;left:0;height:1px;width:100%;transform-origin:50% 100%;transform:scaleY(calc(1 / var(--f7-device-pixel-ratio)))}.md .item-input:not(.item-input-outline) .item-content::before,.md .item-input:not(.item-input-outline).item-content::before{content:'';position:absolute;left:0;top:8px;bottom:8px;right:0;border-radius:var(--f7-input-item-border-radius);background:var(--f7-input-item-bg-color);pointer-events:none}.md .item-input:not(.item-input-outline) .item-content::after,.md .item-input:not(.item-input-outline).item-content::after{transform:scaleY(1)!important;transition-duration:.2s;bottom:8px}.md .item-input:not(.item-input-outline) .item-content.item-input-focused::after,.md .item-input:not(.item-input-outline).item-content.item-input-focused::after{transform:scaleY(2)!important;background:var(--f7-input-focused-border-color,var(--f7-theme-color))}.md .item-input:not(.item-input-outline) .item-content.input-invalid::after,.md .item-input:not(.item-input-outline) .item-content.item-input-invalid::after,.md .item-input:not(.item-input-outline).item-content.input-invalid::after,.md .item-input:not(.item-input-outline).item-content.item-input-invalid::after{transform:scaleY(2)!important;background:var(--f7-input-invalid-border-color,var(--f7-input-error-text-color))}.md .input-outline .item-inner,.md .item-input-outline .item-content .item-inner,.md .item-input-outline.item-content .item-inner{padding-top:16px;padding-bottom:16px}.md .input-outline .item-floating-label,.md .input-outline .item-label,.md .item-input-outline .item-content .item-floating-label,.md .item-input-outline .item-content .item-label,.md .item-input-outline.item-content .item-floating-label,.md .item-input-outline.item-content .item-label{--label-height:calc(var(--f7-label-height) + 8px);margin:calc(0px - 16px - var(--label-height)/ 2) -4px 4px}.md .input-outline .item-floating-label,.md .item-input-outline .item-content .item-floating-label,.md .item-input-outline.item-content .item-floating-label{transform:scale(var(--f7-floating-label-scale)) translateY(calc((var(--f7-input-height)/ 2) + 8px))}.md .input-with-error-message,.md .input-with-info,.md .item-input-with-error-message,.md .item-input-with-info{padding-bottom:24px!important}.md .input-error-message,.md .input-info,.md .item-input-error-message,.md .item-input-info{position:absolute;top:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:100%;right:0}.md .item-input-focused .item-floating-label,.md .item-input-focused .item-label{color:var(--f7-label-focused-text-color,var(--f7-theme-color))}.md .input-focused:not(.input-outline):after,.md .item-input-focused:not(.item-input-outline) .item-input-wrap:after{background:var(--f7-input-focused-border-color,var(--f7-theme-color))}.md .input-focused:not(.input-outline):after,.md .input-invalid:not(.input-outline):after,.md .item-input-focused:not(.item-input-outline) .item-input-wrap:after,.md .item-input-invalid:not(.item-input-outline) .item-input-wrap:after{transform:scaleY(2)!important}.md .input-invalid:not(.input-outline):after,.md .item-input-invalid:not(.item-input-outline) .item-input-wrap:after{background:var(--f7-input-invalid-border-color,var(--f7-input-error-text-color))}.md .item-input-invalid .item-floating-label,.md .item-input-invalid .item-label{color:var(--f7-label-invalid-text-color,var(--f7-input-error-text-color))}.md .input-invalid input,.md .input-invalid select,.md .input-invalid textarea,.md .item-input-invalid input,.md .item-input-invalid select,.md .item-input-invalid textarea{color:var(--f7-input-invalid-text-color,var(--f7-input-text-color))}.md .input-clear-button:after{font-size:var(--f7-input-clear-button-size);content:'delete_round_md';line-height:1}.md .input-clear-button:before{width:48px;height:48px;margin-left:-24px;margin-top:-24px}