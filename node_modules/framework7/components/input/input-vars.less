:root {
  --f7-input-bg-color: transparent;
  --f7-label-font-weight: 400;
  --f7-label-height: 16px;
  --f7-label-font-size: 12px;
  --f7-floating-label-scale: calc(16 / 12);

  --f7-input-padding-left: 0px;
  --f7-input-padding-right: 0px;
  --f7-input-error-text-color: #ff3b30;
  --f7-input-error-font-size: 12px;
  --f7-input-error-line-height: 1.4;
  --f7-input-error-font-weight: 400;
  --f7-input-info-font-size: 12px;
  --f7-input-info-line-height: 1.4;

  --f7-textarea-height: 100px;
  /*
  --f7-input-outline-focused-border-color: var(--f7-theme-color);
  --f7-input-outline-invalid-border-color: var(--f7-input-error-text-color);
  */
  .light-vars({
  });
  .dark-vars({
  });
}
.ios-vars({
  --f7-input-item-bg-color: transparent;
  --f7-input-item-border-radius: 0px;
  --f7-input-height: 40px;
  --f7-input-font-size: 16px;
  --f7-input-placeholder-color: #a9a9a9;
  --f7-textarea-padding-vertical: 8px;

  /*
  --f7-input-focused-border-color: var(--f7-list-item-border-color);
  --f7-input-invalid-border-color: var(--f7-list-item-border-color);
  --f7-input-invalid-text-color: var(--f7-input-error-text-color);
  */
  --f7-label-text-color: inherit;
  /*
  --f7-label-focused-text-color: var(--f7-label-text-color);
  --f7-label-invalid-text-color: var(--f7-label-text-color);
  */
  --f7-input-clear-button-size: 14px;
  --f7-input-outline-border-radius: 8px;
  .light-vars({
    --f7-input-text-color: #000000;
    --f7-input-info-text-color: rgba(0,0,0,0.45);
    --f7-input-clear-button-color: rgba(0,0,0,0.45);
    --f7-input-outline-border-color: #bbb;
  });
  .dark-vars({
    --f7-input-text-color: #fff;
    --f7-input-info-text-color: rgba(255,255,255,0.55);
    --f7-input-clear-button-color: rgba(255,255,255,0.5);
    --f7-input-outline-border-color: #444;
  });
});
.md-vars({
  --f7-input-item-border-radius: 4px 4px 0 0;
  --f7-input-height: 24px;
  --f7-input-font-size: 16px;
  --f7-textarea-padding-vertical: 0px;
  --f7-input-outline-border-radius: 4px;
  /*
  --f7-input-focused-border-color: var(--f7-theme-color);
  --f7-input-invalid-border-color: var(--f7-input-error-text-color);
  --f7-input-invalid-text-color: var(--f7-input-text-color);
  */
  /*
  --f7-label-focused-text-color: var(--f7-theme-color);
  --f7-label-invalid-text-color: var(--f7-input-error-text-color );
  */
  --f7-floating-label-scale: calc(16 / 12);
  --f7-input-clear-button-size: 24px;

  .light-vars({
    --f7-input-info-text-color: rgba(0,0,0,.45);
  });
  .dark-vars({
    --f7-input-info-text-color: rgba(255,255,255,.45);
  });
});

.md-color-vars({
  --f7-input-placeholder-color: var(--f7-md-on-surface-variant);
  --f7-input-item-bg-color: var(--f7-md-surface-variant);
  --f7-input-border-color: var(--f7-md-outline);
  --f7-input-clear-button-color: var(--f7-md-on-surface-variant);
  --f7-input-outline-border-color: var(--f7-md-outline);
  --f7-input-text-color: var(--f7-md-on-surface);
  --f7-label-text-color: var(--f7-md-on-surface-variant);
});
