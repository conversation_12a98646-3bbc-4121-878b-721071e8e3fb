.ios {
  .list-index {
    .list-index-label {
      margin-bottom: calc(-1 * var(--f7-list-index-label-size) / 2);
      margin-right: calc(var(--f7-list-index-width) - 1px);
      border-radius: 50%;
      &:before {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50% 0% 50% 50%;
        content: '';
        background-color: inherit;
        left: 0;
        top: 0;
        transform: rotate(45deg);
        z-index: -1;
      }
    }
  }
}
