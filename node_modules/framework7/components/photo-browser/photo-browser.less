/* === Photo Browser === */
@import './photo-browser-vars.less';

.photo-browser {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 400;
}
.photo-browser-standalone {
  &.modal-in {
    transition-duration: 0ms;
    animation: photo-browser-in 400ms;
  }
  &.modal-out {
    transition-duration: 0ms;
    animation: photo-browser-out 400ms !important;
    &.swipe-close-to-bottom,
    &.swipe-close-to-top {
      animation: none !important;
    }
  }
  &.photo-browser-transitioning {
    transition: 400ms;
    animation: none !important;
  }
}
.photo-browser-popup {
  &.modal-out {
    &.swipe-close-to-bottom,
    &.swipe-close-to-top {
      transition-duration: 300ms;
    }
    &.swipe-close-to-bottom {
      transform: translate3d(0, 100vh, 0);
    }
    &.swipe-close-to-top {
      transform: translate3d(0, -100vh, 0);
    }
  }
}

.photo-browser-page {
  background: none;
  .toolbar {
    transform: none;
  }
}
.photo-browser-popup {
  background: none;
}

.photo-browser-of {
  margin: 0 5px;
}
.photo-browser-captions {
  pointer-events: none;
  position: absolute;
  left: 0;
  width: 100%;
  bottom: var(--f7-safe-area-bottom);
  z-index: 10;
  opacity: 1;
  transition-duration: 400ms;
  &.photo-browser-captions-exposed {
    opacity: 0;
  }
}
.toolbar ~ .toolbar.photo-browser-thumbs {
  bottom: calc(var(--f7-toolbar-height) + var(--f7-safe-area-bottom));
  .page ~ .photo-browser-page:not(.photo-browser-exposed) &,
  .photo-browser-popup:not(.photo-browser-exposed) &,
  .photo-browser-standalone:not(.photo-browser-exposed) & {
    height: var(--f7-toolbar-height);
  }
  .photo-browser-exposed & {
    transform: translate3d(0, calc(var(--f7-toolbar-height) + var(--f7-safe-area-bottom, 0)), 0);
    .swiper {
      height: calc(100% - var(--f7-safe-area-bottom, 0));
    }
  }
}
.navbar + .toolbar.photo-browser-thumbs {
  .swiper {
    height: calc(100% - var(--f7-safe-area-bottom, 0));
  }
}
.toolbar ~ .photo-browser-captions {
  bottom: calc(var(--f7-toolbar-height) + var(--f7-safe-area-bottom));
  transform: translate3d(0, 0px, 0);
  &.photo-browser-captions-exposed {
    transform: translate3d(0, 0px, 0);
  }
}
.toolbar ~ .toolbar ~ .photo-browser-captions {
  bottom: calc(var(--f7-toolbar-height) * 2 + var(--f7-safe-area-bottom));
  &.photo-browser-captions-exposed {
    transform: translate3d(0, var(--f7-toolbar-height), 0);
  }
}
.photo-browser-thumbs {
  transition-property: transform, background-color, color;
  .swiper {
    width: 100%;
    height: 100%;
  }
  .swiper-slide {
    width: auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  img {
    width: auto;
    height: 75%;
    display: block;
    border-radius: 2px;
    min-width: 4px;
  }
  .swiper-slide img {
    transform: translateX(-4px);
    transition-duration: 150ms;
  }
  .swiper-slide-active img {
    transform: scale(1.2);
  }
  .swiper-slide-active ~ .swiper-slide img {
    transform: translateX(4px);
  }
}
.photo-browser-caption {
  box-sizing: border-box;
  transition-duration: 400ms;
  position: absolute;
  bottom: 0;
  left: 0;
  opacity: 0;
  padding: 4px 5px;
  width: 100%;
  text-align: center;
  font-size: var(--f7-photobrowser-caption-font-size);
  font-weight: var(--f7-photobrowser-caption-font-weight);
  .ios-translucent-bars & {
    @supports (backdrop-filter: blur(20px)) {
      backdrop-filter: saturate(180%) blur(var(--f7-bars-translucent-blur));
    }
  }

  &:empty {
    display: none;
  }
  &.photo-browser-caption-active {
    opacity: 1;
  }
  .photo-browser-captions-light & {
    color: var(--f7-photobrowser-caption-light-text-color);
  }
  .photo-browser-captions-dark &,
  .dark & {
    color: var(--f7-photobrowser-caption-dark-text-color);
  }
}

.photo-browser-swiper-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: var(--f7-photobrowser-bg-color);
  transition: 400ms;
  transition-property: background-color;
  touch-action: none;
}

.photo-browser-prev,
.photo-browser-next {
  &.swiper-button-disabled {
    opacity: 0.3;
    pointer-events: none;
  }
}
.photo-browser-slide {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  box-sizing: border-box;
  span.swiper-zoom-container {
    display: none;
  }
  img {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
    display: none;
  }
  &.swiper-slide-active,
  &.swiper-slide-next,
  &.swiper-slide-prev {
    span.swiper-zoom-container {
      display: flex;
    }
    img {
      display: inline;
    }
    &.photo-browser-slide-lazy {
      .preloader {
        display: block;
      }
    }
  }
  iframe {
    width: 100%;
    height: 100%;
  }
  .preloader {
    display: none;
    position: absolute;
    width: 42px;
    height: 42px;
    margin-left: -21px;
    margin-top: -21px;
    left: 50%;
    top: 50%;
  }
}
.navbar-photo-browser .right .link {
  svg,
  i {
    width: 24px;
    height: 24px;
    display: block;
  }
}
.photo-browser-page .navbar-bg,
.navbar-photo-browser .navbar-bg,
.photo-browser-page .toolbar,
.photo-browser-caption {
  background-color: var(--f7-photobrowser-bars-bg-color, rgba(var(--f7-bars-bg-color-rgb), 0.95));
}
.photo-browser-page {
  touch-action: none;
  .navbar,
  .toolbar {
    transform: translate3d(0, 0, 0);
    transition-duration: 400ms;
    color: var(--f7-photobrowser-bars-text-color, var(--f7-bars-text-color));
    a {
      color: var(
        --f7-photobrowser-bars-link-color,
        var(--f7-bars-link-color, var(--f7-theme-color))
      );
    }
  }
}
.photo-browser-exposed {
  .navbar,
  .toolbar:not(.photo-browser-thumbs) {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
  }
  .toolbar:not(.photo-browser-thumbs) ~ .photo-browser-captions,
  .toolbar ~ .toolbar.photo-browser-thumbs ~ .photo-browser-captions {
    transform: translate3d(0, var(--f7-toolbar-height), 0);
  }
  .photo-browser-swiper-container {
    background: var(--f7-photobrowser-exposed-bg-color);
  }
}
.photo-browser-exposed,
.photo-browser-dark {
  .photo-browser-thumbs,
  .toolbar {
    background-color: var(--f7-photobrowser-dark-bars-bg-color);
  }
  .photo-browser-caption {
    color: var(--f7-photobrowser-caption-dark-text-color);
    background-color: var(--f7-photobrowser-dark-bars-bg-color);
  }
}
.view.with-photo-browser-page-exposed {
  .navbar {
    opacity: 0;
    pointer-events: none;
  }
}
.photo-browser-page .toolbar {
  .hairline-remove-top-bottom();
}
.photo-browser-page-dark .navbar-bg,
.navbar-photo-browser-dark .navbar-bg {
  background: var(--f7-photobrowser-dark-bars-bg-color);
  .hairline-remove-top-bottom();
}

.navbar-photo-browser-dark,
.photo-browser-dark .navbar,
.photo-browser-dark .toolbar,
.photo-browser-page-dark .navbar,
.photo-browser-page-dark .toolbar {
  color: var(--f7-photobrowser-dark-bars-text-color);
  a {
    color: var(--f7-photobrowser-dark-bars-link-color);
  }
}

.photo-browser-dark,
.photo-browser-page-dark {
  .photo-browser-swiper-container {
    background: var(--f7-photobrowser-dark-bg-color);
  }
}

@keyframes photo-browser-in {
  0% {
    transform: translate3d(0, 0, 0) scale(0.5);
    opacity: 0;
  }
  50% {
    transform: translate3d(0, 0, 0) scale(1.05);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
}
@keyframes photo-browser-out {
  0% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate3d(0, 0, 0) scale(1.05);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, 0, 0) scale(0.5);
    opacity: 0;
  }
}

.if-ios-theme({
  @import './photo-browser-ios.less';
});
.if-md-theme({
  @import './photo-browser-md.less';
});
