:root {
  --f7-login-screen-blocks-max-width: 480px;
  --f7-login-screen-title-text-align: center;
  --f7-login-screen-title-text-color: inherit;
  --f7-login-screen-title-letter-spacing: 0;
}
.ios-vars({
  --f7-login-screen-blocks-margin-vertical: 25px;
  --f7-login-screen-transition-timing-function: initial;
  --f7-login-screen-transition-duration: 400ms;
  --f7-login-screen-title-font-weight: 600;
  --f7-login-screen-title-font-size: 28px;
  .light-vars({
    --f7-login-screen-content-bg-color: #fff;
    --f7-login-screen-bg-color: #fff;
  });
  .dark-vars({
    --f7-login-screen-bg-color: #000;
    --f7-login-screen-content-bg-color: #000;
  });
});
.md-vars({
  --f7-login-screen-transition-duration: 600ms;
  --f7-login-screen-transition-timing-function: cubic-bezier(0, 1, 0.2, 1);
  --f7-login-screen-blocks-margin-vertical: 24px;
  --f7-login-screen-title-font-weight: 400;
  --f7-login-screen-title-font-size: 28px;
});

.md-color-vars({
  --f7-login-screen-content-bg-color: var(--f7-md-surface);
  --f7-login-screen-bg-color: var(--f7-md-surface);
});
