.ios {
  .messages-title,
  .message-header,
  .message-footer,
  .message-name {
    b {
      font-weight: 600;
    }
  }
  .message-header,
  .message-name {
    margin-bottom: 3px;
  }
  .message-footer {
    margin-top: 3px;
  }
  .message-bubble {
    min-width: 48px;
  }
  .message-image {
    margin: var(--f7-message-bubble-padding-vertical)
      calc(-1 * var(--f7-message-bubble-padding-horizontal));
    &:first-child {
      margin-top: calc(-1 * var(--f7-message-bubble-padding-vertical));
      img {
        border-top-left-radius: var(--f7-message-bubble-border-radius);
        border-top-right-radius: var(--f7-message-bubble-border-radius);
      }
    }
    &:last-child {
      margin-bottom: calc(-1 * var(--f7-message-bubble-padding-vertical));
      img {
        border-bottom-left-radius: var(--f7-message-bubble-border-radius);
        border-bottom-right-radius: var(--f7-message-bubble-border-radius);
      }
    }
  }
  .message-text-header {
    margin-bottom: 3px;
  }
  .message-text-footer {
    margin-top: 3px;
  }
  .message-received {
    margin-left: calc(10px + var(--f7-safe-area-left));
    .message-header,
    .message-footer,
    .message-name {
      margin-left: var(--f7-message-bubble-padding-horizontal);
    }

    .message-bubble {
      padding-left: calc(var(--f7-message-bubble-padding-horizontal) + 6px);
      -webkit-mask-box-image: url("data:image/svg+xml;charset=utf-8,<svg height='35' viewBox='0 0 96 70' width='48' xmlns='http://www.w3.org/2000/svg'><path d='m96 35c1 7-5 37-42 35-37 2-43-28-42-35-1-7 5-37 42-35 37-2 43 28 42 35z'/></svg>")
        50% 42% 46% 56%;
    }
    .message-image {
      margin-left: calc(-1 * (var(--f7-message-bubble-padding-horizontal) + 6px));
    }
    &.message-tail .message-bubble {
      border-radius: var(--f7-message-bubble-border-radius) var(--f7-message-bubble-border-radius)
        var(--f7-message-bubble-border-radius) 0;
    }
    &.message-tail:not(.message-typing) .message-bubble {
      -webkit-mask-box-image: url("data:image/svg+xml;charset=utf-8,<svg height='35' viewBox='0 0 96 70' width='48' xmlns='http://www.w3.org/2000/svg'><path d='m96 35c1 7-5 37-42 35-37 2-43-28-42-35-1-7 5-37 42-35 37-2 43 28 42 35z'/><path d='m0 70c6-2 12-10 12-19v-16l14 27s-8 8-26 8z'/></svg>")
        50% 42% 46% 56%;
      .message-image:last-child img {
        border-bottom-left-radius: 0px;
      }
    }
  }
  .message-sent {
    margin-right: calc(10px + var(--f7-safe-area-right));
    .message-header,
    .message-footer,
    .message-name {
      margin-right: var(--f7-message-bubble-padding-horizontal);
    }
    .message-bubble {
      padding-right: calc(var(--f7-message-bubble-padding-horizontal) + 6px);
      -webkit-mask-box-image: url("data:image/svg+xml;charset=utf-8,<svg height='35' viewBox='0 0 96 70' width='48' xmlns='http://www.w3.org/2000/svg'><path d='m84 35c1 7-5 37-42 35-37 2-43-28-42-35-1-7 5-37 42-35 37-2 43 28 42 35z'/></svg>")
        50% 56% 46% 42%;
    }
    .message-image {
      margin-right: calc(-1 * (var(--f7-message-bubble-padding-horizontal) + 6px));
    }
    &.message-tail .message-bubble {
      border-radius: var(--f7-message-bubble-border-radius) var(--f7-message-bubble-border-radius) 0
        var(--f7-message-bubble-border-radius);
    }
    &.message-tail .message-bubble {
      -webkit-mask-box-image: url("data:image/svg+xml;charset=utf-8,<svg height='35' viewBox='0 0 96 70' width='48' xmlns='http://www.w3.org/2000/svg'><path d='m84 35c1 7-5 37-42 35-37 2-43-28-42-35-1-7 5-37 42-35 37-2 43 28 42 35z'/><path d='m96 70c-6-2-12-10-12-19v-16l-14 27s8 8 26 8z'/></svg>")
        50% 56% 46% 42%;
      .message-image:last-child img {
        border-bottom-right-radius: 0px;
      }
    }
  }

  // Rules
  .message + .message:not(.message-first) {
    margin-top: 1px;
  }

  // Typing
  .message-received.message-typing {
    .message-content {
      &:after,
      &:before {
        content: '';
        position: absolute;
        background: var(--f7-message-received-bg-color);
        border-radius: 50%;
      }
      &:after {
        width: 11px;
        height: 11px;
        left: 4px;
        bottom: 0px;
      }
      &:before {
        width: 6px;
        height: 6px;
        left: -1px;
        bottom: -4px;
      }
    }
  }
  // Typing
  .message-typing-indicator {
    > div {
      width: 9px;
      height: 9px;
      + div {
        .ltr({
          margin-left: 4px;
        });
        .rtl({
          margin-right: 4px;
        });
      }
    }
    > div:nth-child(1) {
      animation: ios-message-typing-indicator 900ms infinite;
    }
    > div:nth-child(2) {
      animation: ios-message-typing-indicator 900ms 150ms infinite;
    }
    > div:nth-child(3) {
      animation: ios-message-typing-indicator 900ms 300ms infinite;
    }
  }
}

@keyframes ios-message-typing-indicator {
  0% {
    opacity: 0.35;
  }
  25% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.2;
  }
}
