/* === Grid === */
@import './grid-vars.less';

@cols: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20;

.grid {
  display: grid;
  &.grid-gap {
    gap: var(--f7-grid-gap);
  }
}
each(@cols, {
  .grid-cols-@{value} {
    grid-template-columns: repeat(@value, minmax(0,1fr));
  }
});
each(@cols, {
  .grid-rows-@{value} {
    grid-template-rows: repeat(@value, minmax(0,1fr));
  }
});
@media (min-width: @breakpoints[xsmall]) {
  each(@cols, {
    .xsmall-grid-cols-@{value} {
      grid-template-columns: repeat(@value, minmax(0,1fr));
    }
    .xsmall-grid-rows-@{value} {
      grid-template-rows: repeat(@value, minmax(0,1fr));
    }
  });
}
@media (min-width: @breakpoints[small]) {
  each(@cols, {
    .small-grid-cols-@{value} {
      grid-template-columns: repeat(@value, minmax(0,1fr));
    }
    .small-grid-rows-@{value} {
      grid-template-rows: repeat(@value, minmax(0,1fr));
    }
  });
}
@media (min-width: @breakpoints[medium]) {
  each(@cols, {
    .medium-grid-cols-@{value} {
      grid-template-columns: repeat(@value, minmax(0,1fr));
    }
    .medium-grid-rows-@{value} {
      grid-template-rows: repeat(@value, minmax(0,1fr));
    }
  });
}
@media (min-width: @breakpoints[large]) {
  each(@cols, {
    .large-grid-cols-@{value} {
      grid-template-columns: repeat(@value, minmax(0,1fr));
    }
    .large-grid-rows-@{value} {
      grid-template-rows: repeat(@value, minmax(0,1fr));
    }
  });
}
@media (min-width: @breakpoints[xlarge]) {
  each(@cols, {
    .xlarge-grid-cols-@{value} {
      grid-template-columns: repeat(@value, minmax(0,1fr));
    }
    .xlarge-grid-rows-@{value} {
      grid-template-rows: repeat(@value, minmax(0,1fr));
    }
  });
}

.if-ios-theme({
  @import './grid-ios.less';
});
.if-md-theme({
  @import './grid-md.less';
});
