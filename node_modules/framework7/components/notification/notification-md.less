.md {
  .notification {
    transform: translate3d(0, -150%, 0);
    &.modal-in {
      transition-duration: 500ms;
      transform: translate3d(0, 0%, 0);
      transition-timing-function: cubic-bezier(0, 0.8, 0.34, 1);
      &.notification-transitioning {
        transition-duration: 200ms;
      }
    }
    &.modal-out {
      animation: none;
      transition-duration: 200ms;
      transition-timing-function: ease-in;
      transform: translate3d(0, -150%, 0);
    }
  }
  .notification-with-icon {
    .notification-icon {
      position: absolute;
      left: var(--f7-notification-padding-horizontal);
      top: var(--f7-notification-padding-vertical);
    }
    .notification-content,
    .notification-header {
      margin-left: calc(var(--f7-notification-icon-size) + 16px);
    }
  }

  .notification-icon {
    margin-right: 8px;
  }
  .notification-subtitle {
    + .notification-text {
      margin-top: 4px;
    }
  }
  .notification-header + .notification-content {
    margin-top: 8px;
  }
  .notification-title-right-text {
    margin-left: 4px;
    &:before {
      content: '';
      width: 3px;
      height: 3px;
      border-radius: 50%;
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      background: var(--f7-notification-title-right-color);
    }
  }
  .notification-close-button {
    width: 16px;
    height: 16px;
    transition-duration: 300ms;

    &:before,
    &:after {
      width: 48px;
      height: 48px;
      left: 50%;
      top: 50%;
      margin-left: -24px;
      margin-top: -24px;
    }
    &:after {
      color: var(--f7-md-on-surface-variant);
      content: 'delete_round_md';
      line-height: 48px;
      font-size: 24px;
    }
  }
}
@keyframes notification-md-in {
  0% {
    transform: translate3d(0, -150%, 0);
  }
  50% {
    transform: translate3d(0, 10%, 0);
  }
  100% {
    transform: translate3d(0, 0%, 0);
  }
}
