.md {
  .page-previous {
    transform: translate3d(0, -20px, 0);
  }
  .page-next {
    pointer-events: none;
    transform: translate3d(0, 128px, 0);
    opacity: 0;
    &.page-next-on-right {
      .ltr({
        transform: translate3d(100%, 0, 0);
        opacity: 1;
      });
      .rtl({
        transform: translate3d(-100%, 0, 0);
        opacity: 1;
      });
    }
  }
  // Animations
  .router-transition-forward {
    .page-next {
      opacity: 1;
      animation: md-page-next-to-current var(--f7-page-transition-duration) forwards
        cubic-bezier(0, 0.8, 0.3, 1);
    }
    .page-current {
      animation: md-page-current-to-previous var(--f7-page-transition-duration) forwards
        cubic-bezier(0, 0.8, 0.3, 1);
    }
  }
  .router-transition-backward {
    .page-current {
      animation:
        md-page-current-to-next calc(var(--f7-page-transition-duration) / 4) forwards
          cubic-bezier(0.8, 0, 1, 0.3),
        md-page-current-to-next-opacity 0ms calc(var(--f7-page-transition-duration) / 4) forwards;
    }
    .page-previous {
      animation: md-page-previous-to-current var(--f7-page-transition-duration) forwards;
    }
  }
}
@keyframes md-page-next-to-current {
  from {
    transform: translate3d(0, 128px, 0);
  }
  to {
    transform: translate3d(0, 0px, 0);
  }
}
@keyframes md-page-current-to-next {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    transform: translate3d(0, 128px, 0);
  }
}
@keyframes md-page-current-to-next-opacity {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes md-page-current-to-previous {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    transform: translate3d(0, -20px, 0);
  }
}
@keyframes md-page-previous-to-current {
  from {
    transform: translate3d(0, -20px, 0);
  }
  to {
    transform: translate3d(0, 0px, 0);
  }
}
