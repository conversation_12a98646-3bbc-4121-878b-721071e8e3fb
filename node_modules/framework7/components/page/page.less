/* === Pages === */
@import './page-vars.less';
.pages {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.page {
  box-sizing: border-box;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transform: none;
  background-color: var(--f7-page-bg-color);
  z-index: 1;
}
.page-with-navbar-large-collapsed {
  --f7-navbar-large-collapse-progress: 1;
}
.page-previous {
  pointer-events: none;
}
.page-content {
  .scrollable();
  box-sizing: border-box;
  height: 100%;
  position: relative;
  z-index: 1;
  padding-top: calc(
    var(--f7-page-navbar-offset, 0px) + var(--f7-page-toolbar-top-offset, 0px) +
      var(--f7-page-subnavbar-offset, 0px) + var(--f7-page-searchbar-offset, 0px) +
      var(--f7-page-content-extra-padding-top, 0px)
  );
  padding-bottom: calc(
    var(--f7-page-toolbar-bottom-offset, 0px) + var(--f7-safe-area-bottom) +
      var(--f7-page-content-extra-padding-bottom, 0px)
  );
}
.page-transitioning {
  &,
  .page-shadow-effect,
  .page-opacity-effect {
    transition-duration: var(--f7-page-transition-duration);
  }
}
.page-transitioning-swipeback {
  &,
  .page-shadow-effect,
  .page-opacity-effect {
    transition-duration: var(--f7-page-swipeback-transition-duration);
  }
}
.router-transition-forward,
.router-transition-backward {
  .page-next,
  .page-current {
    pointer-events: none;
  }
}
.page-fake-shadow() {
  position: absolute;
  top: 0;
  width: 16px;
  bottom: 0;
  z-index: -1;
  content: '';
  opacity: 0;
  .rtl({
    left: 100%;
    background: linear-gradient(to left, rgba(0,0,0,0) 0%, rgba(0,0,0,0) 10%, rgba(0,0,0,0.01) 50%, rgba(0,0,0,0.2) 100%);
  });
  .ltr({
    right: 100%;
    background: linear-gradient(to right, rgba(0,0,0,0) 0%, rgba(0,0,0,0) 10%, rgba(0,0,0,0.01) 50%, rgba(0,0,0,0.2) 100%);
  });
}
.page-fake-opacity() {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.1);
  width: 100%;
  bottom: 0;
  content: '';
  opacity: 0;
  z-index: 10000;
}
.page-shadow-effect {
  .page-fake-shadow();
}
.page-opacity-effect {
  .page-fake-opacity();
}

.if-ios-theme({
  @import './page-ios.less';
});
.if-md-theme({
  @import './page-md.less';
});

.view:not(.view-master-detail) {
  .page-master-stacked {
    display: none;
  }
  .navbar-master-stacked {
    display: none;
  }
}
.view-master-detail {
  .page-master,
  .navbar-master {
    width: var(--f7-page-master-width);
    .rtl({
      left: auto;
      right: 0;
    });
    .ltr({
      --f7-safe-area-right: 0px;
      --f7-safe-area-outer-right: 0px;
      border-right: var(--f7-page-master-border-width) solid var(--f7-page-master-border-color);
    });
    .rtl({
      --f7-safe-area-left: 0px;
      --f7-safe-area-outer-left: 0px;
      border-left: var(--f7-page-master-border-width) solid var(--f7-page-master-border-color);
    });
  }
  .page-master-detail,
  .navbar-master-detail {
    width: calc(100% - var(--f7-page-master-width));
    .ltr({
      --f7-safe-area-left: 0px;
      --f7-safe-area-outer-left: 0px;
      left: var(--f7-page-master-width);
    });
    .rtl({
      --f7-safe-area-right: 0px;
      --f7-safe-area-outer-right: 0px;
      right: var(--f7-page-master-width);
    });
  }
  .page-master-detail > .navbar-master-detail {
    left: 0;
    right: 0;
    width: 100%;
  }
  .page-master {
    z-index: 2;
    transform: none;
    pointer-events: auto;
    &:before,
    &:after {
      display: none;
    }
  }
  &.router-transition {
    .page-master {
      animation: none;
    }
  }
}
.md .router-transition-custom,
.ios .router-transition-custom {
  .page-previous,
  .page-next {
    opacity: 1;
    transform: none;
  }
  &.view-master-detail .page-master {
    animation: none;
  }
}

.page-title {
  position: relative;
  z-index: 10;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--f7-page-title-text-color);
  letter-spacing: var(--f7-page-title-letter-spacing);
  font-size: var(--f7-page-title-font-size);
  font-weight: var(--f7-page-title-font-weight);
  line-height: var(--f7-page-title-line-height);
  padding-left: calc(var(--f7-page-title-padding-left) + var(--f7-safe-area-left));
  padding-right: calc(var(--f7-page-title-padding-right) + var(--f7-safe-area-right));
  padding-top: var(--f7-page-title-padding-vertical);
  padding-bottom: var(--f7-page-title-padding-vertical);
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
}

@import './page-transitions/circle.less';
@import './page-transitions/cover-v.less';
@import './page-transitions/cover.less';
@import './page-transitions/dive.less';
@import './page-transitions/fade.less';
@import './page-transitions/flip.less';
@import './page-transitions/parallax.less';
@import './page-transitions/push.less';
