/* === Searchbar === */
@import './searchbar-vars.less';

.searchbar {
  --f7-link-highlight-color: var(--f7-link-highlight-black);
  .dark & {
    --f7-link-highlight-color: var(--f7-link-highlight-white);
  }
  width: 100%;
  position: relative;
  z-index: 200;
  height: var(--f7-searchbar-height);
  background-color: var(--f7-searchbar-bg-color, var(--f7-bars-bg-color));
  input[type='search']::-webkit-search-decoration {
    display: none;
  }
  .ios-translucent-bars(var(--f7-searchbar-bg-color-rgb, var(--f7-bars-bg-color-rgb)));
  .ios .subnavbar & {
    background-color: transparent;
    backdrop-filter: none;
    .hairline-remove(bottom);
  }
  &.no-outline {
    &:after {
      display: none !important;
    }
  }

  .hairline(bottom, var(--f7-searchbar-border-color, var(--f7-bars-border-color)));

  .page > &:not(.searchbar-inline) {
    z-index: 600;
  }
  input[type='text'],
  input[type='search'] {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    display: block;
    border: var(--f7-searchbar-input-border-width) solid var(--f7-searchbar-input-border-color);
    appearance: none;
    font-family: inherit;
    font-weight: normal;
    color: var(--f7-searchbar-input-text-color);
    font-size: var(--f7-searchbar-input-font-size);
    background-color: var(--f7-searchbar-input-bg-color);
    border-radius: var(--f7-searchbar-input-border-radius);
    position: relative;
    padding: 0;
    .ltr({
      padding-left: calc(var(--f7-searchbar-input-padding-horizontal) + var(--f7-searchbar-input-extra-padding-left, 0px));
      padding-right: calc(var(--f7-searchbar-input-padding-horizontal) + var(--f7-searchbar-input-extra-padding-right, 0px));;
    });
    .rtl({
      padding-left: calc(var(--f7-searchbar-input-padding-horizontal) + var(--f7-searchbar-input-extra-padding-right, 0px));;
      padding-right: calc(var(--f7-searchbar-input-padding-horizontal) + var(--f7-searchbar-input-extra-padding-left, 0px));
    });
    &::placeholder {
      color: var(--f7-searchbar-placeholder-color);
      opacity: 1;
    }
  }
  input::-webkit-search-cancel-button {
    appearance: none;
  }
  .searchbar-input-wrap {
    flex-shrink: 1;
    width: 100%;
    height: var(--f7-searchbar-input-height);
    position: relative;
  }
  a {
    color: var(--f7-searchbar-link-color, var(--f7-bars-link-color, var(--f7-theme-color)));
  }
  .page > &:not(.searchbar-inline) {
    position: absolute;
    left: 0;
    top: 0;
  }
  .page-content &:not(.searchbar-inline) {
    border-radius: var(--f7-searchbar-in-page-content-border-radius);
    margin: var(--f7-searchbar-in-page-content-margin);
    width: auto;
    box-shadow: var(--f7-searchbar-in-page-content-box-shadow);
    .searchbar-inner,
    input[type='text'],
    input[type='search'] {
      border-radius: var(
        --f7-searchbar-in-page-content-input-border-radius,
        var(--f7-searchbar-input-border-radius)
      );
    }
  }
  .input-clear-button {
    color: var(--f7-searchbar-input-clear-button-color, var(--f7-input-clear-button-color));
  }
}
.searchbar-expandable {
  --f7-searchbar-expandable-size: var(--f7-searchbar-height);
  position: absolute;
  transition-duration: 300ms;
  pointer-events: none;
}
.navbar .searchbar-expandable {
  .hairline-remove(bottom);
  background: transparent;
}
.navbar .searchbar.searchbar-expandable {
  --f7-searchbar-expandable-size: calc(var(--f7-navbar-height) + var(--f7-safe-area-top));
  .searchbar-inner {
    top: var(--f7-safe-area-top);
    height: calc(100% - var(--f7-safe-area-top));
  }
}
.toolbar .searchbar.searchbar-expandable {
  --f7-searchbar-expandable-size: var(--f7-toolbar-height);
}
.subnavbar .searchbar.searchbar-expandable {
  --f7-searchbar-expandable-size: var(--f7-subnavbar-height);
}
.tabbar-icons .searchbar.searchbar-expandable {
  --f7-searchbar-expandable-size: var(--f7-tabbar-icons-height);
}
.searchbar-inner {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 calc(var(--f7-searchbar-inner-padding-right) + var(--f7-safe-area-right)) 0
    calc(var(--f7-searchbar-inner-padding-left) + var(--f7-safe-area-left));
}
.searchbar-disable-button {
  cursor: pointer;
  pointer-events: none;
  appearance: none;
  background: none;
  border: none;
  outline: 0;
  padding: 0;
  margin: 0;
  width: auto;
  opacity: 0;
}
.searchbar-icon {
  pointer-events: none;
  background-position: center;
  background-repeat: no-repeat;
  &:after {
    color: var(--f7-searchbar-search-icon-color);
    .core-icons-font();
  }
}
.searchbar-backdrop {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  opacity: 0;
  pointer-events: none;
  transition-duration: 300ms;
  transform: translate3d(0, 0, 0);
  background: var(--f7-searchbar-backdrop-bg-color);
  &.searchbar-backdrop-in {
    opacity: 1;
    pointer-events: auto;
  }
  .page-content > & {
    position: fixed;
  }
}
.searchbar-not-found {
  display: none;
}
.hidden-by-searchbar {
  &,
  .list &,
  .list.li&,
  .list li& {
    display: none !important;
  }
}
.navbar.with-searchbar-expandable-enabled-no-transition,
.navbar.with-searchbar-expandable-enabled-no-transition {
  --f7-navbar-large-collapse-progress: 1;
}
.navbar.with-searchbar-expandable-enabled,
.navbar.with-searchbar-expandable-enabled {
  --f7-navbar-large-collapse-progress: 1;
  .navbar-bg,
  .title-large,
  .title-large-text {
    transition-duration: 300ms;
  }
}
.navbar.with-searchbar-expandable-closing,
.navbar.with-searchbar-expandable-closing {
  .navbar-bg,
  .title-large,
  .title-large-text {
    transition-duration: 300ms;
  }
}

.page-content.with-searchbar-expandable-enabled {
  height: calc(100% + var(--f7-navbar-large-title-height));
  transform: translateY(calc(-1 * var(--f7-navbar-large-title-height)));
  transition-duration: 300ms;
  transition-property: transform;
}
.page-content.with-searchbar-expandable-closing {
  transition-duration: 300ms;
}

// Relation with page
.navbar ~ .page:not(.no-navbar) > .searchbar,
.navbars ~ .page:not(.no-navbar) > .searchbar,
.page > .navbar ~ .searchbar {
  top: calc(var(--f7-navbar-height) + var(--f7-safe-area-top));
}

.navbar ~ .page-with-navbar-large:not(.no-navbar) .searchbar,
.navbars ~ .page-with-navbar-large:not(.no-navbar) .searchbar,
.page-with-navbar-large .navbar ~ .searchbar,
.page-with-navbar-large .navbar ~ * .searchbar {
  top: calc(
    var(--f7-navbar-height) + var(--f7-navbar-large-title-height) + var(--f7-safe-area-top)
  );
  transform: translate3d(
    0,
    calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)),
    0
  );
}
.navbars ~ .page-with-navbar-large:not(.no-navbar) .page-content .searchbar,
.page-with-navbar-large .page-content .searchbar {
  top: 0;
  transform: none;
}

.searchbar ~ * {
  --f7-page-searchbar-offset: var(--f7-searchbar-height);
}

// Toolbar
.page > .toolbar-top,
.ios .page > .toolbar-top-ios,
.md .page > .toolbar-top-md {
  ~ .searchbar {
    top: var(--f7-toolbar-height);
  }
}
.page > .tabbar-icons.toolbar-top,
.ios .page > .tabbar-icons.toolbar-top-ios,
.md .page > .tabbar-icons.toolbar-top-md {
  ~ .searchbar {
    top: var(--f7-tabbar-icons-height);
  }
}
.page > .navbar ~ .toolbar-top,
.ios .page > .navbar ~ .toolbar-top-ios,
.md .page > .navbar ~ .toolbar-top-md {
  ~ .searchbar {
    top: calc(var(--f7-navbar-height) + var(--f7-toolbar-height) + var(--f7-safe-area-top));
  }
}
.page > .navbar ~ .tabbar-icons.toolbar-top,
.ios .page > .navbar ~ .tabbar-icons.toolbar-top-ios,
.md .page > .navbar ~ .tabbar-icons.toolbar-top-md {
  ~ .searchbar {
    top: calc(var(--f7-navbar-height) + var(--f7-tabbar-icons-height) + var(--f7-safe-area-top));
  }
}

.searchbar.searchbar-inline {
  width: auto;
  height: auto;
  background-color: transparent;
  background-image: none;

  &:after,
  &:before {
    display: none !important;
  }
  .searchbar-input-wrap {
    height: var(--f7-searchbar-inline-input-height, var(--f7-searchbar-input-height));
  }
  .searchbar-inner {
    padding: 0;
    position: static;
    width: auto;
    height: auto;
  }
  input[type='text'],
  input[type='search'] {
    font-size: var(--f7-searchbar-inline-input-font-size, var(--f7-searchbar-input-font-size));
    border-radius: var(
      --f7-searchbar-inline-input-border-radius,
      var(--f7-searchbar-input-border-radius)
    );
    .ltr({
      padding-left: calc(var(--f7-searchbar-inline-input-padding-horizontal, var(--f7-searchbar-input-padding-horizontal)) + var(--f7-searchbar-input-extra-padding-left, 0px));
      padding-right: calc(var(--f7-searchbar-inline-input-padding-horizontal, var(--f7-searchbar-input-padding-horizontal)) + var(--f7-searchbar-input-extra-padding-right, 0px));;
    });
    .rtl({
      padding-left: calc(var(--f7-searchbar-inline-input-padding-horizontal, var(--f7-searchbar-input-padding-horizontal)) + var(--f7-searchbar-input-extra-padding-right, 0px));;
      padding-right: calc(var(--f7-searchbar-inline-input-padding-horizontal, var(--f7-searchbar-input-padding-horizontal)) + var(--f7-searchbar-input-extra-padding-left, 0px));
    });
  }
}

.if-ios-theme({
  @import './searchbar-ios.less';
});
.if-md-theme({
  @import './searchbar-md.less';
});
