/* === List View === */
@import './list-vars.less';
:root {
  --f7-list-chevron-icon-left: 'chevron_left';
  --f7-list-chevron-icon-right: 'chevron_right';
}

.list {
  position: relative;
  z-index: 1;
  font-size: var(--f7-list-font-size);
  margin: var(--f7-list-margin-vertical) 0;
  .item-row() {
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
  }
  .item-cell() {
    display: block;
    align-self: center;
    box-sizing: border-box;
  }

  &-strong ul,
  .ios &-strong-ios ul,
  .md &-strong-md ul {
    background: var(--f7-list-strong-bg-color);
  }
  &-outline ul,
  .ios &-outline-ios ul,
  .md &-outline-md ul {
    .hairline(top, var(--f7-list-outline-border-color));
    .hairline(bottom, var(--f7-list-outline-border-color));
  }

  &-outline.inset,
  .ios &-outline-ios.inset,
  .ios &-outline-ios.inset-ios,
  .md &-outline-md.inset,
  .md &-outline-md.inset-md {
    border: 1px solid var(--f7-list-outline-inset-border-color);
  }

  ul {
    list-style: none;
    margin: 0;
    padding: 0;
    position: relative;

    ul {
      .ltr({
        padding-left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-list-in-list-padding-left));
      });
      .rtl({
        padding-right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-list-in-list-padding-left));
      });
      .hairline-remove-top-bottom();
    }
  }
  li {
    position: relative;
    box-sizing: border-box;
  }

  .item-media {
    display: flex;
    flex-shrink: 0;
    flex-wrap: nowrap;
    align-items: center;
    box-sizing: border-box;
    padding-bottom: var(--f7-list-item-padding-vertical);
    padding-top: var(--f7-list-item-padding-vertical);
    position: relative;
    + .item-inner {
      .ltr({
        margin-left: var(--f7-list-item-media-margin);
      });
      .rtl({
        margin-right: var(--f7-list-item-media-margin);
      });
    }
  }
  .item-media {
    i + i,
    i + img {
      .ltr({
        margin-left: var(--f7-list-item-media-icons-margin);
      });
      .rtl({
        margin-right: var(--f7-list-item-media-icons-margin);
      });
    }
  }
  .item-after {
    .ltr({
      padding-left: var(--f7-list-item-after-padding);
    });
    .rtl({
      padding-right: var(--f7-list-item-after-padding);
    });
  }
  .item-inner {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    min-width: 0;
    .item-row();
    align-items: center;
    align-self: stretch;
    padding-top: var(--f7-list-item-padding-vertical);
    padding-bottom: var(--f7-list-item-padding-vertical);
    min-height: calc(var(--f7-list-item-min-height));
    .ltr({
      padding-right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
    });
    .rtl({
      padding-left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
    });
  }
  .item-title {
    min-width: 0;
    flex-shrink: 1;
    white-space: var(--f7-list-item-title-white-space);
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    font-size: var(--f7-list-item-title-font-size);
    font-weight: var(--f7-list-item-title-font-weight);
    color: var(--f7-list-item-title-text-color);
    line-height: var(--f7-list-item-title-line-height);
  }
  .item-after {
    white-space: nowrap;
    flex-shrink: 0;
    display: flex;
    font-size: var(--f7-list-item-after-font-size);
    font-weight: var(--f7-list-item-after-font-weight);
    color: var(--f7-list-item-after-text-color);
    line-height: var(--f7-list-item-after-line-height);
    .ltr({
      margin-left: auto;
    });
    .rtl({
      margin-right: auto;
    });
  }
  .item-header,
  .item-footer {
    white-space: normal;
  }
  .item-header {
    color: var(--f7-list-item-header-text-color);
    font-size: var(--f7-list-item-header-font-size);
    font-weight: var(--f7-list-item-header-font-weight);
    line-height: var(--f7-list-item-header-line-height);
  }
  .item-footer {
    color: var(--f7-list-item-footer-text-color);
    font-size: var(--f7-list-item-footer-font-size);
    font-weight: var(--f7-list-item-footer-font-weight);
    line-height: var(--f7-list-item-footer-line-height);
  }
  .item-link,
  label.item-content,
  .list-button {
    transition-duration: 300ms;
    transition-property: background-color, color;
    position: relative;
    overflow: hidden;
    z-index: 0;
    cursor: pointer;
  }
  .item-link,
  .list-button {
    display: block;
  }
  .item-link,
  label.item-content {
    color: inherit;
    &.active-state {
      background-color: var(--f7-list-link-pressed-bg-color);
    }
  }
  .item-link {
    .item-inner {
      .ltr({
        padding-right: calc(var(--f7-list-chevron-icon-area) + var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
      });
      .rtl({
        padding-left: calc(var(--f7-list-chevron-icon-area) + var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
      });
    }
  }

  .item-content {
    .item-row();
    box-sizing: border-box;
    align-items: center;
    min-height: calc(var(--f7-list-item-min-height));
    .ltr({
      padding-left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
    });
    .rtl({
      padding-right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
    });
  }

  .item-subtitle {
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    max-width: 100%;
    text-overflow: ellipsis;
    font-size: var(--f7-list-item-subtitle-font-size);
    font-weight: var(--f7-list-item-subtitle-font-weight);
    color: var(--f7-list-item-subtitle-text-color);
    line-height: var(--f7-list-item-subtitle-line-height);
  }
  .item-text {
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    /* autoprefixer: ignore next */
    -webkit-line-clamp: var(--f7-list-item-text-max-lines);
    /* autoprefixer: ignore next */
    -webkit-box-orient: vertical;
    display: -webkit-box;
    font-size: var(--f7-list-item-text-font-size);
    font-weight: var(--f7-list-item-text-font-weight);
    color: var(--f7-list-item-text-text-color);
    line-height: var(--f7-list-item-text-line-height);
    max-height: calc(var(--f7-list-item-text-line-height) * var(--f7-list-item-text-max-lines));
  }
  .item-title-row {
    position: relative;
    .item-row();
    .item-after {
      align-self: center;
    }
  }

  // Last-childs
  li {
    &:last-child {
      .list-button {
        .hairline-remove(bottom);
      }
    }
    &:last-child,
    &:last-child li:last-child {
      > .item-inner,
      > .item-content > .item-inner,
      > .swipeout-content > .item-content > .item-inner,
      > .item-link > .item-content > .item-inner {
        .hairline-remove(bottom);
      }
    }
    li:last-child,
    &:last-child li {
      .item-inner {
        .hairline(bottom, var(--f7-list-item-border-color));
      }
    }
  }
}
.list-button {
  --f7-touch-ripple-color: var(--f7-link-touch-ripple-color, rgba(var(--f7-theme-color-rgb), 0.25));
  padding: 0 var(--f7-list-item-padding-horizontal);
  line-height: var(--f7-list-item-min-height);
  color: var(--f7-list-button-text-color, var(--f7-theme-color));
  font-size: var(--f7-list-button-font-size);
  font-weight: var(--f7-list-button-font-weight);
  text-align: var(--f7-list-button-text-align);

  .ios &.active-state {
    background-color: var(--f7-list-button-pressed-bg-color, rgba(var(--f7-theme-color-rgb), 0.15));
  }
  .md &.active-state {
    background-color: var(--f7-list-button-pressed-bg-color, transparent);
  }
  .list-dividers &,
  .ios .list-dividers-ios &,
  .md .list-dividers-md & {
    .hairline(bottom, var(--f7-list-button-border-color));
  }
}
.list-button[class*='color-'] {
  --f7-list-button-text-color: var(--f7-theme-color);
}
.simple-list {
  li {
    position: relative;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-content: center;
    line-height: var(--f7-list-item-min-height);
    height: var(--f7-list-item-min-height);
    padding-left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
    padding-right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
  }
  li:after {
    left: var(--f7-list-item-padding-horizontal);
    right: 0;
    width: auto;
    .ltr({
      left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
      right: 0;
    });
    .rtl({
      right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
      left: 0;
    });
  }
  li:last-child {
    .hairline-remove(bottom);
  }
}
.links-list {
  li {
    z-index: 1;
  }
  a {
    transition-duration: 300ms;
    transition-property: background-color;
    display: block;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: space-between;
    box-sizing: border-box;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
    height: var(--f7-list-item-min-height);
    color: inherit;
    .ripple-wave {
      z-index: 0;
    }
    &:after {
      width: auto;
    }
    &.active-state {
      background-color: var(--f7-list-link-pressed-bg-color);
    }
  }
  .ltr({
    a {
      padding-left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
      padding-right: calc(var(--f7-list-chevron-icon-area) + var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
    }
    a:after {
      left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
      right: 0;
    }
  });
  .rtl({
    a {
      padding-right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
      padding-left: calc(var(--f7-list-chevron-icon-area) + var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
    }
    a:after {
      right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
      left: 0;
    }
  });
  li:last-child a {
    .hairline-remove(bottom);
  }
}
.list-dividers,
.ios .list-dividers-ios,
.md .list-dividers-md {
  &.simple-list li,
  &.links-list a,
  .item-inner {
    .hairline(bottom, var(--f7-list-item-border-color));
  }
}

.md .media-list:not(.inset):not(.inset-md):not(.menu-list) .item-content,
.md .media-list:not(.inset):not(.inset-md):not(.menu-list) li > .item-link,
.md .list:not(.inset):not(.inset-md):not(.menu-list) .media-item .item-content,
.md .list:not(.inset):not(.inset-md):not(.menu-list) .media-item > .item-link {
  margin-left: calc(var(--f7-list-item-padding-horizontal) / 2);
  margin-right: calc(var(--f7-list-item-padding-horizontal) / 2);
  border-radius: 16px;
}
.md .media-list:not(.inset):not(.inset-md):not(.menu-list) li > .item-link > .item-content,
.md .list:not(.inset):not(.inset-md):not(.menu-list) .media-item > .item-link > .item-content {
  margin-left: 0;
  margin-right: 0;
}
.md .media-list:not(.inset):not(.inset-md):not(.menu-list) .item-content,
.md .list:not(.inset):not(.inset-md):not(.menu-list) .media-item .item-content {
  .ltr({
    padding-left: calc(var(--f7-list-item-padding-horizontal) / 2 + var(--f7-safe-area-left));
  });
  .rtl({
    padding-right: calc(var(--f7-list-item-padding-horizontal) / 2 + var(--f7-safe-area-right));
  });
}

.media-list,
li.media-item {
  --f7-list-item-padding-vertical: var(--f7-list-media-item-padding-vertical);
  --f7-list-item-padding-horizontal: var(--f7-list-media-item-padding-horizontal);
  .item-title {
    font-weight: var(
      --f7-list-media-item-title-font-weight,
      var(--f7-list-item-title-font-weight, inherit)
    );
  }
  .item-inner {
    display: block;
    align-self: stretch;
  }
  .item-media {
    img {
      display: block;
    }
  }
  .ltr({
    .item-link .item-inner {
      padding-right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
    }
    .item-link .item-title-row {
      padding-right: calc(var(--f7-list-chevron-icon-area));
    }
  });
  .rtl({
    .item-link .item-inner {
      padding-left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
    }
    .item-link .item-title-row {
      padding-left: calc(var(--f7-list-chevron-icon-area));
    }
  });
}
.media-list.chevron-center .item-link .item-inner,
.media-list .chevron-center .item-link .item-inner,
.media-list .item-link.chevron-center .item-inner,
li.media-item.chevron-center .item-link .item-inner,
li.media-item .item-link.chevron-center .item-inner,
li.media-item .chevron-center .item-link .item-inner {
  .ltr({
    padding-right: calc(var(--f7-list-chevron-icon-area) + var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
  });
  .rtl({
    padding-left: calc(var(--f7-list-chevron-icon-area) + var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
  });
}
.media-list.chevron-center .item-title-row,
.media-list .chevron-center .item-title-row,
li.media-item.chevron-center .item-title-row,
li.media-item .chevron-center .item-title-row {
  .ltr({
    padding-right: 0;
  });
  .rtl({
    padding-left: 0;
  });
}
.list .item-link .item-inner,
.links-list a,
.media-list .item-link .item-title-row,
li.media-item .item-link .item-title-row,
.media-list.chevron-center .item-link .item-inner,
.media-list .chevron-center .item-link .item-inner,
.media-list .item-link.chevron-center .item-inner,
li.media-item.chevron-center .item-link .item-inner,
li.media-item .chevron-center .item-link .item-inner,
li.media-item .item-link.chevron-center .item-inner {
  &:before {
    .core-icons-font();
    position: absolute;
    top: 50%;
    width: 8px;
    height: 14px;
    margin-top: -7px;
    font-size: var(--f7-list-chevron-icon-font-size);
    line-height: 14px;
    color: var(--f7-list-chevron-icon-color);
    pointer-events: none;
    .ltr({
      right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
      content: var(--f7-list-chevron-icon-right);
    });
    .rtl({
      left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
      content: var(--f7-list-chevron-icon-left);
    });
  }
}

.media-list.chevron-center .item-title-row,
.media-list .chevron-center .item-title-row,
li.media-item.chevron-center .item-title-row,
li.media-item .chevron-center .item-title-row {
  &:before {
    display: none;
  }
}

.media-list .item-link .item-inner,
li.media-item .item-link .item-inner {
  &:before {
    display: none;
  }
}
.media-list .item-link .item-title-row,
li.media-item .item-link .item-title-row {
  &:before {
    .ltr({
      right: 0;
    });
    .rtl({
      left: 0;
    });
  }
}
.list-group {
  ul {
    &:after,
    &:before {
      z-index: 25 !important;
    }
  }
  + .list-group ul {
    .hairline-remove(top);
  }
}
li.list-group-title,
.list-group-title {
  margin-top: -1px;
  white-space: nowrap;
  position: relative;
  max-width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  z-index: 15;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-left));
  padding-right: calc(var(--f7-list-item-padding-horizontal) + var(--f7-safe-area-right));
  box-sizing: border-box;
  display: flex;
  align-items: center;
  align-content: center;
  height: var(--f7-list-group-title-height);
  color: var(--f7-list-group-title-text-color);
  font-size: var(--f7-list-group-title-font-size);
  font-weight: var(--f7-list-group-title-font-weight);
  background-color: var(--f7-list-group-title-bg-color);
  line-height: var(--f7-list-group-title-line-height);
  .hairline(top, var(--f7-list-group-title-border-color));
  .hairline-remove(bottom);
}
.list-group > .list-group-title,
.list-group > ul > .list-group-title {
  top: 0;
  margin-top: 0;
  position: sticky;
  z-index: 20;
  .page-with-navbar-large & {
    top: calc(-1 * var(--f7-navbar-large-title-height));
  }
}

.list.inset,
.ios .list.inset-ios,
.md .list.inset-md {
  margin-left: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-left));
  margin-right: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-right));
  border-radius: var(--f7-list-inset-border-radius);
  --f7-safe-area-left: 0px;
  --f7-safe-area-right: 0px;
  .block-title {
    margin-left: 0;
    margin-right: 0;
  }
  ul {
    border-radius: var(--f7-list-inset-border-radius);
    .hairline-remove-top-bottom();
  }
  li.swipeout:first-child,
  li:first-child > a {
    border-radius: var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius) 0 0;
  }
  li.swipeout:last-child,
  li:last-child > a {
    border-radius: 0 0 var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius);
  }
  li.swipeout:first-child:last-child,
  li:first-child:last-child > a {
    border-radius: var(--f7-list-inset-border-radius);
  }
}
each(@breakpoints, {
  @media (min-width:@value) {
    .list.@{key}-inset,
    .ios .list.@{key}-inset-ios,
    .md .list.@{key}-inset-md {
      margin-left: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-left));
      margin-right: calc(var(--f7-list-inset-side-margin) + var(--f7-safe-area-outer-right));
      border-radius: var(--f7-list-inset-border-radius);
      --f7-safe-area-left: 0px;
      --f7-safe-area-right: 0px;
      .block-title {
        margin-left: 0;
        margin-right: 0;
      }
      ul {
        border-radius: var(--f7-list-inset-border-radius);
        .hairline-remove-top-bottom();
      }
      li:first-child > a {
        border-radius: var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius) 0 0;
      }
      li:last-child > a {
        border-radius: 0 0 var(--f7-list-inset-border-radius) var(--f7-list-inset-border-radius);
      }
      li:first-child:last-child > a {
        border-radius: var(--f7-list-inset-border-radius);
      }
    }

    .list.@{key}-inset.list-outline,
    .ios .list.@{key}-inset-ios.list-outline,
    .ios .list.@{key}-inset-ios.list-outline-ios,
    .md .list.@{key}-inset-md.list-outline,
    .md .list.@{key}-inset-md.list-outline-md {
      ul {
        border: 1px solid var(--f7-list-outline-inset-border-color);
      }

    }
  }
});

.list.no-chevron,
.list .no-chevron,
.menu-list {
  --f7-list-chevron-icon-color: transparent;
  --f7-list-chevron-icon-area: 0px;
}

.menu-list {
  --f7-list-font-size: var(--f7-menu-list-font-size);
  --f7-list-item-title-font-size: var(--f7-menu-list-item-title-font-size);
  --f7-list-item-title-font-weight: var(--f7-menu-list-item-title-font-weight);
  --f7-list-item-subtitle-font-size: var(--f7-menu-list-item-subtitle-font-size);
  --f7-list-item-text-font-size: var(--f7-menu-list-item-text-font-size);
  --f7-list-item-after-font-size: var(--f7-menu-list-item-after-font-size);
  --f7-list-item-min-height: var(--f7-menu-list-item-min-height);

  .item-header,
  .item-footer,
  .item-title,
  .item-after,
  .item-text,
  .item-subtitle {
    transition-duration: 300ms;
  }

  li:not(.list-group-title) {
    padding-top: 4px;
    padding-bottom: 4px;
    &:first-child {
      padding-top: 4px;
    }
    &:last-child {
      padding-bottom: 4px;
    }
  }
  .item-link {
    margin-left: var(--f7-menu-list-offset);
    margin-right: var(--f7-menu-list-offset);
    border-radius: var(--f7-menu-list-border-radius) !important;
    color: var(--f7-menu-list-item-text-color);
    background-color: var(--f7-menu-list-item-bg-color);
  }

  .item-inner:after {
    display: none;
  }
  .item-selected .item-link,
  .item-selected.item-link,
  .tab-link-active .item-link,
  .tab-link-active.item-link {
    color: var(--f7-menu-list-item-selected-text-color);
    background-color: var(--f7-menu-list-item-selected-bg-color);
  }
  .item-selected {
    .item-header,
    .item-footer,
    .item-title,
    .item-after,
    .item-text,
    .item-subtitle {
      color: var(--f7-menu-list-item-selected-text-color);
    }
  }
}

.if-ios-theme({
  @import './list-ios.less';
});
.if-md-theme({
  @import './list-md.less';
});
