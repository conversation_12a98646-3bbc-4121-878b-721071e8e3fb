{"name": "framework7", "version": "8.3.4", "description": "Full featured mobile HTML framework for building iOS & Android apps", "type": "module", "exports": {".": {"import": "./framework7.esm.js", "types": "./framework7.d.ts"}, "./core": {"import": "./framework7.esm.js", "types": "./framework7.d.ts"}, "./bundle": {"import": "./framework7-bundle.esm.js", "types": "./framework7.d.ts"}, "./lite": {"import": "./framework7-lite.esm.js", "types": "./framework7.d.ts"}, "./lite/bundle": {"import": "./framework7-lite-bundle.esm.js", "types": "./framework7.d.ts"}, "./lite-bundle": {"import": "./framework7-lite-bundle.esm.js", "types": "./framework7.d.ts"}, "./less": "./framework7.less", "./less/bundle": "./framework7-bundle.less", "./css": "./framework7.css", "./css/rtl": "./framework7-rtl.css", "./css/bundle": "./framework7-bundle.css", "./css/bundle/rtl": "./framework7-bundle-rtl.css", "./types": "./framework7-types.d.ts", "./shared/*": "./shared/*", "./components/dialog": "./components/dialog/dialog.js", "./components/dialog/less": "./components/dialog/dialog.less", "./components/dialog/css": "./components/dialog/dialog.css", "./components/dialog/css/rtl": "./components/dialog/dialog-rtl.css", "./components/popup": "./components/popup/popup.js", "./components/popup/less": "./components/popup/popup.less", "./components/popup/css": "./components/popup/popup.css", "./components/popup/css/rtl": "./components/popup/popup-rtl.css", "./components/login-screen": "./components/login-screen/login-screen.js", "./components/login-screen/less": "./components/login-screen/login-screen.less", "./components/login-screen/css": "./components/login-screen/login-screen.css", "./components/login-screen/css/rtl": "./components/login-screen/login-screen-rtl.css", "./components/popover": "./components/popover/popover.js", "./components/popover/less": "./components/popover/popover.less", "./components/popover/css": "./components/popover/popover.css", "./components/popover/css/rtl": "./components/popover/popover-rtl.css", "./components/actions": "./components/actions/actions.js", "./components/actions/less": "./components/actions/actions.less", "./components/actions/css": "./components/actions/actions.css", "./components/actions/css/rtl": "./components/actions/actions-rtl.css", "./components/sheet": "./components/sheet/sheet.js", "./components/sheet/less": "./components/sheet/sheet.less", "./components/sheet/css": "./components/sheet/sheet.css", "./components/sheet/css/rtl": "./components/sheet/sheet-rtl.css", "./components/toast": "./components/toast/toast.js", "./components/toast/less": "./components/toast/toast.less", "./components/toast/css": "./components/toast/toast.css", "./components/toast/css/rtl": "./components/toast/toast-rtl.css", "./components/preloader": "./components/preloader/preloader.js", "./components/preloader/less": "./components/preloader/preloader.less", "./components/preloader/css": "./components/preloader/preloader.css", "./components/preloader/css/rtl": "./components/preloader/preloader-rtl.css", "./components/progressbar": "./components/progressbar/progressbar.js", "./components/progressbar/less": "./components/progressbar/progressbar.less", "./components/progressbar/css": "./components/progressbar/progressbar.css", "./components/progressbar/css/rtl": "./components/progressbar/progressbar-rtl.css", "./components/sortable": "./components/sortable/sortable.js", "./components/sortable/less": "./components/sortable/sortable.less", "./components/sortable/css": "./components/sortable/sortable.css", "./components/sortable/css/rtl": "./components/sortable/sortable-rtl.css", "./components/swipeout": "./components/swipeout/swipeout.js", "./components/swipeout/less": "./components/swipeout/swipeout.less", "./components/swipeout/css": "./components/swipeout/swipeout.css", "./components/swipeout/css/rtl": "./components/swipeout/swipeout-rtl.css", "./components/accordion": "./components/accordion/accordion.js", "./components/accordion/less": "./components/accordion/accordion.less", "./components/accordion/css": "./components/accordion/accordion.css", "./components/accordion/css/rtl": "./components/accordion/accordion-rtl.css", "./components/contacts-list": "./components/contacts-list/contacts-list.js", "./components/contacts-list/less": "./components/contacts-list/contacts-list.less", "./components/contacts-list/css": "./components/contacts-list/contacts-list.css", "./components/contacts-list/css/rtl": "./components/contacts-list/contacts-list-rtl.css", "./components/virtual-list": "./components/virtual-list/virtual-list.js", "./components/virtual-list/less": "./components/virtual-list/virtual-list.less", "./components/virtual-list/css": "./components/virtual-list/virtual-list.css", "./components/virtual-list/css/rtl": "./components/virtual-list/virtual-list-rtl.css", "./components/list-index": "./components/list-index/list-index.js", "./components/list-index/less": "./components/list-index/list-index.less", "./components/list-index/css": "./components/list-index/list-index.css", "./components/list-index/css/rtl": "./components/list-index/list-index-rtl.css", "./components/timeline": "./components/timeline/timeline.js", "./components/timeline/less": "./components/timeline/timeline.less", "./components/timeline/css": "./components/timeline/timeline.css", "./components/timeline/css/rtl": "./components/timeline/timeline-rtl.css", "./components/tabs": "./components/tabs/tabs.js", "./components/tabs/less": "./components/tabs/tabs.less", "./components/tabs/css": "./components/tabs/tabs.css", "./components/tabs/css/rtl": "./components/tabs/tabs-rtl.css", "./components/panel": "./components/panel/panel.js", "./components/panel/less": "./components/panel/panel.less", "./components/panel/css": "./components/panel/panel.css", "./components/panel/css/rtl": "./components/panel/panel-rtl.css", "./components/card": "./components/card/card.js", "./components/card/less": "./components/card/card.less", "./components/card/css": "./components/card/card.css", "./components/card/css/rtl": "./components/card/card-rtl.css", "./components/chip": "./components/chip/chip.js", "./components/chip/less": "./components/chip/chip.less", "./components/chip/css": "./components/chip/chip.css", "./components/chip/css/rtl": "./components/chip/chip-rtl.css", "./components/form": "./components/form/form.js", "./components/form/less": "./components/form/form.less", "./components/form/css": "./components/form/form.css", "./components/form/css/rtl": "./components/form/form-rtl.css", "./components/input": "./components/input/input.js", "./components/input/less": "./components/input/input.less", "./components/input/css": "./components/input/input.css", "./components/input/css/rtl": "./components/input/input-rtl.css", "./components/checkbox": "./components/checkbox/checkbox.js", "./components/checkbox/less": "./components/checkbox/checkbox.less", "./components/checkbox/css": "./components/checkbox/checkbox.css", "./components/checkbox/css/rtl": "./components/checkbox/checkbox-rtl.css", "./components/radio": "./components/radio/radio.js", "./components/radio/less": "./components/radio/radio.less", "./components/radio/css": "./components/radio/radio.css", "./components/radio/css/rtl": "./components/radio/radio-rtl.css", "./components/toggle": "./components/toggle/toggle.js", "./components/toggle/less": "./components/toggle/toggle.less", "./components/toggle/css": "./components/toggle/toggle.css", "./components/toggle/css/rtl": "./components/toggle/toggle-rtl.css", "./components/range": "./components/range/range.js", "./components/range/less": "./components/range/range.less", "./components/range/css": "./components/range/range.css", "./components/range/css/rtl": "./components/range/range-rtl.css", "./components/stepper": "./components/stepper/stepper.js", "./components/stepper/less": "./components/stepper/stepper.less", "./components/stepper/css": "./components/stepper/stepper.css", "./components/stepper/css/rtl": "./components/stepper/stepper-rtl.css", "./components/smart-select": "./components/smart-select/smart-select.js", "./components/smart-select/less": "./components/smart-select/smart-select.less", "./components/smart-select/css": "./components/smart-select/smart-select.css", "./components/smart-select/css/rtl": "./components/smart-select/smart-select-rtl.css", "./components/grid": "./components/grid/grid.js", "./components/grid/less": "./components/grid/grid.less", "./components/grid/css": "./components/grid/grid.css", "./components/grid/css/rtl": "./components/grid/grid-rtl.css", "./components/calendar": "./components/calendar/calendar.js", "./components/calendar/less": "./components/calendar/calendar.less", "./components/calendar/css": "./components/calendar/calendar.css", "./components/calendar/css/rtl": "./components/calendar/calendar-rtl.css", "./components/picker": "./components/picker/picker.js", "./components/picker/less": "./components/picker/picker.less", "./components/picker/css": "./components/picker/picker.css", "./components/picker/css/rtl": "./components/picker/picker-rtl.css", "./components/infinite-scroll": "./components/infinite-scroll/infinite-scroll.js", "./components/infinite-scroll/less": "./components/infinite-scroll/infinite-scroll.less", "./components/infinite-scroll/css": "./components/infinite-scroll/infinite-scroll.css", "./components/infinite-scroll/css/rtl": "./components/infinite-scroll/infinite-scroll-rtl.css", "./components/pull-to-refresh": "./components/pull-to-refresh/pull-to-refresh.js", "./components/pull-to-refresh/less": "./components/pull-to-refresh/pull-to-refresh.less", "./components/pull-to-refresh/css": "./components/pull-to-refresh/pull-to-refresh.css", "./components/pull-to-refresh/css/rtl": "./components/pull-to-refresh/pull-to-refresh-rtl.css", "./components/data-table": "./components/data-table/data-table.js", "./components/data-table/less": "./components/data-table/data-table.less", "./components/data-table/css": "./components/data-table/data-table.css", "./components/data-table/css/rtl": "./components/data-table/data-table-rtl.css", "./components/fab": "./components/fab/fab.js", "./components/fab/less": "./components/fab/fab.less", "./components/fab/css": "./components/fab/fab.css", "./components/fab/css/rtl": "./components/fab/fab-rtl.css", "./components/searchbar": "./components/searchbar/searchbar.js", "./components/searchbar/less": "./components/searchbar/searchbar.less", "./components/searchbar/css": "./components/searchbar/searchbar.css", "./components/searchbar/css/rtl": "./components/searchbar/searchbar-rtl.css", "./components/messages": "./components/messages/messages.js", "./components/messages/less": "./components/messages/messages.less", "./components/messages/css": "./components/messages/messages.css", "./components/messages/css/rtl": "./components/messages/messages-rtl.css", "./components/messagebar": "./components/messagebar/messagebar.js", "./components/messagebar/less": "./components/messagebar/messagebar.less", "./components/messagebar/css": "./components/messagebar/messagebar.css", "./components/messagebar/css/rtl": "./components/messagebar/messagebar-rtl.css", "./components/swiper": "./components/swiper/swiper.js", "./components/swiper/less": "./components/swiper/swiper.less", "./components/swiper/css": "./components/swiper/swiper.css", "./components/swiper/css/rtl": "./components/swiper/swiper-rtl.css", "./components/photo-browser": "./components/photo-browser/photo-browser.js", "./components/photo-browser/less": "./components/photo-browser/photo-browser.less", "./components/photo-browser/css": "./components/photo-browser/photo-browser.css", "./components/photo-browser/css/rtl": "./components/photo-browser/photo-browser-rtl.css", "./components/notification": "./components/notification/notification.js", "./components/notification/less": "./components/notification/notification.less", "./components/notification/css": "./components/notification/notification.css", "./components/notification/css/rtl": "./components/notification/notification-rtl.css", "./components/autocomplete": "./components/autocomplete/autocomplete.js", "./components/autocomplete/less": "./components/autocomplete/autocomplete.less", "./components/autocomplete/css": "./components/autocomplete/autocomplete.css", "./components/autocomplete/css/rtl": "./components/autocomplete/autocomplete-rtl.css", "./components/tooltip": "./components/tooltip/tooltip.js", "./components/tooltip/less": "./components/tooltip/tooltip.less", "./components/tooltip/css": "./components/tooltip/tooltip.css", "./components/tooltip/css/rtl": "./components/tooltip/tooltip-rtl.css", "./components/gauge": "./components/gauge/gauge.js", "./components/gauge/less": "./components/gauge/gauge.less", "./components/gauge/css": "./components/gauge/gauge.css", "./components/gauge/css/rtl": "./components/gauge/gauge-rtl.css", "./components/skeleton": "./components/skeleton/skeleton.js", "./components/skeleton/less": "./components/skeleton/skeleton.less", "./components/skeleton/css": "./components/skeleton/skeleton.css", "./components/skeleton/css/rtl": "./components/skeleton/skeleton-rtl.css", "./components/color-picker": "./components/color-picker/color-picker.js", "./components/color-picker/less": "./components/color-picker/color-picker.less", "./components/color-picker/css": "./components/color-picker/color-picker.css", "./components/color-picker/css/rtl": "./components/color-picker/color-picker-rtl.css", "./components/treeview": "./components/treeview/treeview.js", "./components/treeview/less": "./components/treeview/treeview.less", "./components/treeview/css": "./components/treeview/treeview.css", "./components/treeview/css/rtl": "./components/treeview/treeview-rtl.css", "./components/text-editor": "./components/text-editor/text-editor.js", "./components/text-editor/less": "./components/text-editor/text-editor.less", "./components/text-editor/css": "./components/text-editor/text-editor.css", "./components/text-editor/css/rtl": "./components/text-editor/text-editor-rtl.css", "./components/pie-chart": "./components/pie-chart/pie-chart.js", "./components/pie-chart/less": "./components/pie-chart/pie-chart.less", "./components/pie-chart/css": "./components/pie-chart/pie-chart.css", "./components/pie-chart/css/rtl": "./components/pie-chart/pie-chart-rtl.css", "./components/area-chart": "./components/area-chart/area-chart.js", "./components/area-chart/less": "./components/area-chart/area-chart.less", "./components/area-chart/css": "./components/area-chart/area-chart.css", "./components/area-chart/css/rtl": "./components/area-chart/area-chart-rtl.css", "./components/typography": "./components/typography/typography.js", "./components/typography/less": "./components/typography/typography.less", "./components/typography/css": "./components/typography/typography.css", "./components/typography/css/rtl": "./components/typography/typography-rtl.css", "./components/breadcrumbs": "./components/breadcrumbs/breadcrumbs.js", "./components/breadcrumbs/less": "./components/breadcrumbs/breadcrumbs.less", "./components/breadcrumbs/css": "./components/breadcrumbs/breadcrumbs.css", "./components/breadcrumbs/css/rtl": "./components/breadcrumbs/breadcrumbs-rtl.css"}, "typings": "framework7.d.ts", "typesVersions": {"*": {".": ["./framework7.d.ts"], "components/dialog": ["./components/dialog/dialog.d.ts"], "components/popup": ["./components/popup/popup.d.ts"], "components/login-screen": ["./components/login-screen/login-screen.d.ts"], "components/popover": ["./components/popover/popover.d.ts"], "components/actions": ["./components/actions/actions.d.ts"], "components/sheet": ["./components/sheet/sheet.d.ts"], "components/toast": ["./components/toast/toast.d.ts"], "components/preloader": ["./components/preloader/preloader.d.ts"], "components/progressbar": ["./components/progressbar/progressbar.d.ts"], "components/sortable": ["./components/sortable/sortable.d.ts"], "components/swipeout": ["./components/swipeout/swipeout.d.ts"], "components/accordion": ["./components/accordion/accordion.d.ts"], "components/contacts-list": ["./components/contacts-list/contacts-list.d.ts"], "components/virtual-list": ["./components/virtual-list/virtual-list.d.ts"], "components/list-index": ["./components/list-index/list-index.d.ts"], "components/timeline": ["./components/timeline/timeline.d.ts"], "components/tabs": ["./components/tabs/tabs.d.ts"], "components/panel": ["./components/panel/panel.d.ts"], "components/card": ["./components/card/card.d.ts"], "components/chip": ["./components/chip/chip.d.ts"], "components/form": ["./components/form/form.d.ts"], "components/input": ["./components/input/input.d.ts"], "components/checkbox": ["./components/checkbox/checkbox.d.ts"], "components/radio": ["./components/radio/radio.d.ts"], "components/toggle": ["./components/toggle/toggle.d.ts"], "components/range": ["./components/range/range.d.ts"], "components/stepper": ["./components/stepper/stepper.d.ts"], "components/smart-select": ["./components/smart-select/smart-select.d.ts"], "components/grid": ["./components/grid/grid.d.ts"], "components/calendar": ["./components/calendar/calendar.d.ts"], "components/picker": ["./components/picker/picker.d.ts"], "components/infinite-scroll": ["./components/infinite-scroll/infinite-scroll.d.ts"], "components/pull-to-refresh": ["./components/pull-to-refresh/pull-to-refresh"], "components/data-table": ["./components/data-table/data-table.d.ts"], "components/fab": ["./components/fab/fab.d.ts"], "components/searchbar": ["./components/searchbar/searchbar.d.ts"], "components/messages": ["./components/messages/messages.d.ts"], "components/messagebar": ["./components/messagebar/messagebar.d.ts"], "components/swiper": ["./components/swiper/swiper.d.ts"], "components/photo-browser": ["./components/photo-browser/photo-browser.d.ts"], "components/notification": ["./components/notification/notification.d.ts"], "components/autocomplete": ["./components/autocomplete/autocomplete.d.ts"], "components/tooltip": ["./components/tooltip/tooltip.d.ts"], "components/skeleton": ["./components/skeleton/skeleton.d.ts"], "components/color-picker": ["./components/color-picker/color-picker.d.ts"], "components/treeview": ["./components/treeview/treeview.d.ts"], "components/text-editor": ["./components/text-editor/text-editor.d.ts"], "components/typography": ["./components/typography/typography.d.ts"], "lite": ["./framework7.d.ts"], "lite/bundle": ["./framework7.d.ts"], "types": ["./framework7-types.d.ts"]}}, "sideEffects": ["*.less", "*.css", "shared/dom7.js"], "repository": {"type": "git", "url": "https://github.com/framework7io/framework7.git"}, "keywords": ["mobile", "framework", "framework7", "<PERSON><PERSON>", "ios", "iphone", "ipad", "apple", "phonegap", "capacitor", "native", "touch", "appstore", "app", "f7", "material", "android", "google", "googleplay"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/framework7io/framework7/issues"}, "homepage": "https://framework7.io/", "funding": {"type": "patreon", "url": "https://www.patreon.com/framework7"}, "dependencies": {"dom7": "^4.0.6", "htm": "^3.1.1", "path-to-regexp": "^6.2.0", "skeleton-elements": "^4.0.1", "ssr-window": "^4.0.2", "swiper": "^10.2.0"}, "releaseDate": "September 18, 2024"}