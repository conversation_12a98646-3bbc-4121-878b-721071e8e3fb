/**
 * Framework7 8.3.4
 * Full featured mobile HTML framework for building iOS & Android apps
 * https://framework7.io/
 *
 * Copyright 2014-2024 <PERSON>
 *
 * Released under the MIT License
 *
 * Released on: September 18, 2024
 */

@import (reference) './less/mixins.less';
@import (reference) './less/vars.less';

@includeIosTheme: true;
@includeMdTheme: true;

@includeDarkTheme: true;
@includeLightTheme: true;

@rtl: false;

// Core
@import './components/app/app.less';
@import './components/statusbar/statusbar.less';
@import './components/view/view.less';
@import './components/page/page.less';
@import './components/link/link.less';
@import './components/navbar/navbar.less';
@import './components/toolbar/toolbar.less';
@import './components/subnavbar/subnavbar.less';
@import './components/block/block.less';
@import './components/list/list.less';
@import './components/badge/badge.less';
@import './components/button/button.less';
@import './components/touch-ripple/touch-ripple.less';
@import './components/icon/icon.less';
@import './components/modal/modal.less';

@import './components/dialog/dialog.less';
@import './components/popup/popup.less';
@import './components/login-screen/login-screen.less';
@import './components/popover/popover.less';
@import './components/actions/actions.less';
@import './components/sheet/sheet.less';
@import './components/toast/toast.less';
@import './components/preloader/preloader.less';
@import './components/progressbar/progressbar.less';
@import './components/sortable/sortable.less';
@import './components/swipeout/swipeout.less';
@import './components/accordion/accordion.less';
@import './components/contacts-list/contacts-list.less';
@import './components/virtual-list/virtual-list.less';
@import './components/list-index/list-index.less';
@import './components/timeline/timeline.less';
@import './components/tabs/tabs.less';
@import './components/panel/panel.less';
@import './components/card/card.less';
@import './components/chip/chip.less';
@import './components/form/form.less';
@import './components/input/input.less';
@import './components/checkbox/checkbox.less';
@import './components/radio/radio.less';
@import './components/toggle/toggle.less';
@import './components/range/range.less';
@import './components/stepper/stepper.less';
@import './components/smart-select/smart-select.less';
@import './components/grid/grid.less';
@import './components/calendar/calendar.less';
@import './components/picker/picker.less';
@import './components/infinite-scroll/infinite-scroll.less';
@import './components/pull-to-refresh/pull-to-refresh.less';
@import './components/data-table/data-table.less';
@import './components/fab/fab.less';
@import './components/searchbar/searchbar.less';
@import './components/messages/messages.less';
@import './components/messagebar/messagebar.less';
@import './components/swiper/swiper.less';
@import './components/photo-browser/photo-browser.less';
@import './components/notification/notification.less';
@import './components/autocomplete/autocomplete.less';
@import './components/tooltip/tooltip.less';
@import './components/gauge/gauge.less';
@import './components/skeleton/skeleton.less';
@import './components/color-picker/color-picker.less';
@import './components/treeview/treeview.less';
@import './components/text-editor/text-editor.less';
@import './components/pie-chart/pie-chart.less';
@import './components/area-chart/area-chart.less';
@import './components/breadcrumbs/breadcrumbs.less';
@import './components/typography/typography.less';
