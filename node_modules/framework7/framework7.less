/**
 * Framework7 8.3.4
 * Full featured mobile HTML framework for building iOS & Android apps
 * https://framework7.io/
 *
 * Copyright 2014-2024 <PERSON>
 *
 * Released under the MIT License
 *
 * Released on: September 18, 2024
 */

@import (reference) './less/mixins.less';
@import (reference) './less/vars.less';

@includeIosTheme: true;
@includeMdTheme: true;

@includeDarkTheme: true;
@includeLightTheme: true;

@rtl: false;

// Core
@import './components/app/app.less';
@import './components/statusbar/statusbar.less';
@import './components/view/view.less';
@import './components/page/page.less';
@import './components/link/link.less';
@import './components/navbar/navbar.less';
@import './components/toolbar/toolbar.less';
@import './components/subnavbar/subnavbar.less';
@import './components/block/block.less';
@import './components/list/list.less';
@import './components/badge/badge.less';
@import './components/button/button.less';
@import './components/touch-ripple/touch-ripple.less';
@import './components/icon/icon.less';
@import './components/modal/modal.less';

//IMPORT_COMPONENTS
