import{c as createShadow}from"../shared/create-shadow.min.mjs";import{e as effectInit}from"../shared/effect-init.min.mjs";import{e as effectTarget}from"../shared/effect-target.min.mjs";import{e as effectVirtualTransitionEnd}from"../shared/effect-virtual-transition-end.min.mjs";import{k as getSlideTransformEl}from"../shared/utils.min.mjs";function EffectFlip(e){let{swiper:t,extendParams:s,on:r}=e;s({flipEffect:{slideShadows:!0,limitRotation:!0}});const a=(e,s)=>{let r=t.isHorizontal()?e.querySelector(".swiper-slide-shadow-left"):e.querySelector(".swiper-slide-shadow-top"),a=t.isHorizontal()?e.querySelector(".swiper-slide-shadow-right"):e.querySelector(".swiper-slide-shadow-bottom");r||(r=createShadow("flip",e,t.isHorizontal()?"left":"top")),a||(a=createShadow("flip",e,t.isHorizontal()?"right":"bottom")),r&&(r.style.opacity=Math.max(-s,0)),a&&(a.style.opacity=Math.max(s,0))};effectInit({effect:"flip",swiper:t,on:r,setTranslate:()=>{const{slides:e,rtlTranslate:s}=t,r=t.params.flipEffect;for(let i=0;i<e.length;i+=1){const o=e[i];let l=o.progress;t.params.flipEffect.limitRotation&&(l=Math.max(Math.min(o.progress,1),-1));const f=o.swiperSlideOffset;let n=-180*l,p=0,d=t.params.cssMode?-f-t.translate:-f,m=0;t.isHorizontal()?s&&(n=-n):(m=d,d=0,p=-n,n=0),o.style.zIndex=-Math.abs(Math.round(l))+e.length,r.slideShadows&&a(o,l);const c=`translate3d(${d}px, ${m}px, 0px) rotateX(${p}deg) rotateY(${n}deg)`;effectTarget(r,o).style.transform=c}},setTransition:e=>{const s=t.slides.map((e=>getSlideTransformEl(e)));s.forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach((t=>{t.style.transitionDuration=`${e}ms`}))})),effectVirtualTransitionEnd({swiper:t,duration:e,transformElements:s})},recreateShadows:()=>{t.params.flipEffect,t.slides.forEach((e=>{let s=e.progress;t.params.flipEffect.limitRotation&&(s=Math.max(Math.min(e.progress,1),-1)),a(e,s)}))},getEffectParams:()=>t.params.flipEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!t.params.cssMode})})}export{EffectFlip as default};
//# sourceMappingURL=effect-flip.min.mjs.map