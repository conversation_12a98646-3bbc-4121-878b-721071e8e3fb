import{c as createShadow}from"../shared/create-shadow.min.mjs";import{e as effectInit}from"../shared/effect-init.min.mjs";import{e as effectTarget}from"../shared/effect-target.min.mjs";import{e as effectVirtualTransitionEnd}from"../shared/effect-virtual-transition-end.min.mjs";import{k as getSlideTransformEl}from"../shared/utils.min.mjs";function EffectCreative(e){let{swiper:t,extendParams:s,on:r}=e;s({creativeEffect:{limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}});const a=e=>"string"==typeof e?e:`${e}px`;effectInit({effect:"creative",swiper:t,on:r,setTranslate:()=>{const{slides:e,wrapperEl:s,slidesSizesGrid:r}=t,i=t.params.creativeEffect,{progressMultiplier:o}=i,l=t.params.centeredSlides;if(l){const e=r[0]/2-t.params.slidesOffsetBefore||0;s.style.transform=`translateX(calc(50% - ${e}px))`}for(let s=0;s<e.length;s+=1){const r=e[s],n=r.progress,c=Math.min(Math.max(r.progress,-i.limitProgress),i.limitProgress);let f=c;l||(f=Math.min(Math.max(r.originalProgress,-i.limitProgress),i.limitProgress));const m=r.swiperSlideOffset,p=[t.params.cssMode?-m-t.translate:-m,0,0],d=[0,0,0];let h=!1;t.isHorizontal()||(p[1]=p[0],p[0]=0);let g={translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1};c<0?(g=i.next,h=!0):c>0&&(g=i.prev,h=!0),p.forEach(((e,t)=>{p[t]=`calc(${e}px + (${a(g.translate[t])} * ${Math.abs(c*o)}))`})),d.forEach(((e,t)=>{d[t]=g.rotate[t]*Math.abs(c*o)})),r.style.zIndex=-Math.abs(Math.round(n))+e.length;const w=p.join(", "),y=`rotateX(${d[0]}deg) rotateY(${d[1]}deg) rotateZ(${d[2]}deg)`,u=f<0?`scale(${1+(1-g.scale)*f*o})`:`scale(${1-(1-g.scale)*f*o})`,v=f<0?1+(1-g.opacity)*f*o:1-(1-g.opacity)*f*o,E=`translate3d(${w}) ${y} ${u}`;if(h&&g.shadow||!h){let e=r.querySelector(".swiper-slide-shadow");if(!e&&g.shadow&&(e=createShadow("creative",r)),e){const t=i.shadowPerProgress?c*(1/i.limitProgress):c;e.style.opacity=Math.min(Math.max(Math.abs(t),0),1)}}const M=effectTarget(i,r);M.style.transform=E,M.style.opacity=v,g.origin&&(M.style.transformOrigin=g.origin)}},setTransition:e=>{const s=t.slides.map((e=>getSlideTransformEl(e)));s.forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow").forEach((t=>{t.style.transitionDuration=`${e}ms`}))})),effectVirtualTransitionEnd({swiper:t,duration:e,transformElements:s,allSlides:!0})},perspective:()=>t.params.creativeEffect.perspective,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!t.params.cssMode})})}export{EffectCreative as default};
//# sourceMappingURL=effect-creative.min.mjs.map