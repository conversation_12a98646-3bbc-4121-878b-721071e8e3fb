import{g as getDocument}from"../shared/ssr-window.esm.min.mjs";function Autoplay(e){let a,t,{swiper:n,extendParams:r,on:i,emit:o,params:s}=e;n.autoplay={running:!1,paused:!1,timeLeft:0},r({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let p,l,u,d,y,m,c,g=s&&s.autoplay?s.autoplay.delay:3e3,v=s&&s.autoplay?s.autoplay.delay:3e3,T=(new Date).getTime;function w(e){n&&!n.destroyed&&n.wrapperEl&&e.target===n.wrapperEl&&(n.wrapperEl.removeEventListener("transitionend",w),O())}const E=()=>{if(n.destroyed||!n.autoplay.running)return;n.autoplay.paused?l=!0:l&&(v=p,l=!1);const e=n.autoplay.paused?p:T+v-(new Date).getTime();n.autoplay.timeLeft=e,o("autoplayTimeLeft",e,e/g),t=requestAnimationFrame((()=>{E()}))},f=e=>{if(n.destroyed||!n.autoplay.running)return;cancelAnimationFrame(t),E();let r=void 0===e?n.params.autoplay.delay:e;g=n.params.autoplay.delay,v=n.params.autoplay.delay;const i=(()=>{let e;if(e=n.virtual&&n.params.virtual.enabled?n.slides.filter((e=>e.classList.contains("swiper-slide-active")))[0]:n.slides[n.activeIndex],!e)return;return parseInt(e.getAttribute("data-swiper-autoplay"),10)})();!Number.isNaN(i)&&i>0&&void 0===e&&(r=i,g=i,v=i),p=r;const s=n.params.speed,l=()=>{n&&!n.destroyed&&(n.params.autoplay.reverseDirection?!n.isBeginning||n.params.loop||n.params.rewind?(n.slidePrev(s,!0,!0),o("autoplay")):n.params.autoplay.stopOnLastSlide||(n.slideTo(n.slides.length-1,s,!0,!0),o("autoplay")):!n.isEnd||n.params.loop||n.params.rewind?(n.slideNext(s,!0,!0),o("autoplay")):n.params.autoplay.stopOnLastSlide||(n.slideTo(0,s,!0,!0),o("autoplay")),n.params.cssMode&&(T=(new Date).getTime(),requestAnimationFrame((()=>{f()}))))};return r>0?(clearTimeout(a),a=setTimeout((()=>{l()}),r)):requestAnimationFrame((()=>{l()})),r},b=()=>{n.autoplay.running=!0,f(),o("autoplayStart")},L=()=>{n.autoplay.running=!1,clearTimeout(a),cancelAnimationFrame(t),o("autoplayStop")},D=(e,t)=>{if(n.destroyed||!n.autoplay.running)return;clearTimeout(a),e||(c=!0);const r=()=>{o("autoplayPause"),n.params.autoplay.waitForTransition?n.wrapperEl.addEventListener("transitionend",w):O()};if(n.autoplay.paused=!0,t)return m&&(p=n.params.autoplay.delay),m=!1,void r();const i=p||n.params.autoplay.delay;p=i-((new Date).getTime()-T),n.isEnd&&p<0&&!n.params.loop||(p<0&&(p=0),r())},O=()=>{n.isEnd&&p<0&&!n.params.loop||n.destroyed||!n.autoplay.running||(T=(new Date).getTime(),c?(c=!1,f(p)):f(),n.autoplay.paused=!1,o("autoplayResume"))},A=()=>{if(n.destroyed||!n.autoplay.running)return;const e=getDocument();"hidden"===e.visibilityState&&(c=!0,D(!0)),"visible"===e.visibilityState&&O()},F=e=>{"mouse"===e.pointerType&&(c=!0,n.animating||n.autoplay.paused||D(!0))},S=e=>{"mouse"===e.pointerType&&n.autoplay.paused&&O()};i("init",(()=>{n.params.autoplay.enabled&&(n.params.autoplay.pauseOnMouseEnter&&(n.el.addEventListener("pointerenter",F),n.el.addEventListener("pointerleave",S)),getDocument().addEventListener("visibilitychange",A),T=(new Date).getTime(),b())})),i("destroy",(()=>{n.el.removeEventListener("pointerenter",F),n.el.removeEventListener("pointerleave",S),getDocument().removeEventListener("visibilitychange",A),n.autoplay.running&&L()})),i("beforeTransitionStart",((e,a,t)=>{!n.destroyed&&n.autoplay.running&&(t||!n.params.autoplay.disableOnInteraction?D(!0,!0):L())})),i("sliderFirstMove",(()=>{!n.destroyed&&n.autoplay.running&&(n.params.autoplay.disableOnInteraction?L():(u=!0,d=!1,c=!1,y=setTimeout((()=>{c=!0,d=!0,D(!0)}),200)))})),i("touchEnd",(()=>{if(!n.destroyed&&n.autoplay.running&&u){if(clearTimeout(y),clearTimeout(a),n.params.autoplay.disableOnInteraction)return d=!1,void(u=!1);d&&n.params.cssMode&&O(),d=!1,u=!1}})),i("slideChange",(()=>{!n.destroyed&&n.autoplay.running&&(m=!0)})),Object.assign(n.autoplay,{start:b,stop:L,pause:D,resume:O})}export{Autoplay as default};
//# sourceMappingURL=autoplay.min.mjs.map