import{n as nextTick,i as elementTransitionEnd}from"../shared/utils.min.mjs";function Controller(t){let{swiper:e,extendParams:n,on:r}=t;function o(t,e){const n=function(){let t,e,n;return(r,o)=>{for(e=-1,t=r.length;t-e>1;)n=t+e>>1,r[n]<=o?e=n:t=n;return t}}();let r,o;return this.x=t,this.y=e,this.lastIndex=t.length-1,this.interpolate=function(t){return t?(o=n(this.x,t),r=o-1,(t-this.x[r])*(this.y[o]-this.y[r])/(this.x[o]-this.x[r])+this.y[r]):0},this}function l(){e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)}n({controller:{control:void 0,inverse:!1,by:"slide"}}),e.controller={control:void 0},r("beforeInit",(()=>{if("undefined"!=typeof window&&("string"==typeof e.params.controller.control||e.params.controller.control instanceof HTMLElement)){const t=document.querySelector(e.params.controller.control);if(t&&t.swiper)e.controller.control=t.swiper;else if(t){const n=r=>{e.controller.control=r.detail[0],e.update(),t.removeEventListener("init",n)};t.addEventListener("init",n)}}else e.controller.control=e.params.controller.control})),r("update",(()=>{l()})),r("resize",(()=>{l()})),r("observerUpdate",(()=>{l()})),r("setTranslate",((t,n,r)=>{e.controller.control&&!e.controller.control.destroyed&&e.controller.setTranslate(n,r)})),r("setTransition",((t,n,r)=>{e.controller.control&&!e.controller.control.destroyed&&e.controller.setTransition(n,r)})),Object.assign(e.controller,{setTranslate:function(t,n){const r=e.controller.control;let l,s;const i=e.constructor;function a(t){if(t.destroyed)return;const n=e.rtlTranslate?-e.translate:e.translate;"slide"===e.params.controller.by&&(!function(t){e.controller.spline=e.params.loop?new o(e.slidesGrid,t.slidesGrid):new o(e.snapGrid,t.snapGrid)}(t),s=-e.controller.spline.interpolate(-n)),s&&"container"!==e.params.controller.by||(l=(t.maxTranslate()-t.minTranslate())/(e.maxTranslate()-e.minTranslate()),!Number.isNaN(l)&&Number.isFinite(l)||(l=1),s=(n-e.minTranslate())*l+t.minTranslate()),e.params.controller.inverse&&(s=t.maxTranslate()-s),t.updateProgress(s),t.setTranslate(s,e),t.updateActiveIndex(),t.updateSlidesClasses()}if(Array.isArray(r))for(let t=0;t<r.length;t+=1)r[t]!==n&&r[t]instanceof i&&a(r[t]);else r instanceof i&&n!==r&&a(r)},setTransition:function(t,n){const r=e.constructor,o=e.controller.control;let l;function s(n){n.destroyed||(n.setTransition(t,e),0!==t&&(n.transitionStart(),n.params.autoHeight&&nextTick((()=>{n.updateAutoHeight()})),elementTransitionEnd(n.wrapperEl,(()=>{o&&n.transitionEnd()}))))}if(Array.isArray(o))for(l=0;l<o.length;l+=1)o[l]!==n&&o[l]instanceof r&&s(o[l]);else o instanceof r&&n!==o&&s(o)}})}export{Controller as default};
//# sourceMappingURL=controller.min.mjs.map