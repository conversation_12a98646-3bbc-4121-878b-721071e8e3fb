import{c as classesToSelector}from"../shared/classes-to-selector.min.mjs";import{c as createElement,g as elementIndex}from"../shared/utils.min.mjs";function A11y(e){let{swiper:a,extendParams:t,on:i}=e;t({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null}}),a.a11y={clicked:!1};let n=null;function s(e){const a=n;0!==a.length&&(a.innerHTML="",a.innerHTML=e)}const r=e=>(Array.isArray(e)?e:[e]).filter((e=>!!e));function o(e){(e=r(e)).forEach((e=>{e.setAttribute("tabIndex","0")}))}function l(e){(e=r(e)).forEach((e=>{e.setAttribute("tabIndex","-1")}))}function c(e,a){(e=r(e)).forEach((e=>{e.setAttribute("role",a)}))}function d(e,a){(e=r(e)).forEach((e=>{e.setAttribute("aria-roledescription",a)}))}function p(e,a){(e=r(e)).forEach((e=>{e.setAttribute("aria-label",a)}))}function g(e){(e=r(e)).forEach((e=>{e.setAttribute("aria-disabled",!0)}))}function u(e){(e=r(e)).forEach((e=>{e.setAttribute("aria-disabled",!1)}))}function f(e){if(13!==e.keyCode&&32!==e.keyCode)return;const t=a.params.a11y,i=e.target;a.pagination&&a.pagination.el&&(i===a.pagination.el||a.pagination.el.contains(e.target))&&!e.target.matches(classesToSelector(a.params.pagination.bulletClass))||(a.navigation&&a.navigation.nextEl&&i===a.navigation.nextEl&&(a.isEnd&&!a.params.loop||a.slideNext(),a.isEnd?s(t.lastSlideMessage):s(t.nextSlideMessage)),a.navigation&&a.navigation.prevEl&&i===a.navigation.prevEl&&(a.isBeginning&&!a.params.loop||a.slidePrev(),a.isBeginning?s(t.firstSlideMessage):s(t.prevSlideMessage)),a.pagination&&i.matches(classesToSelector(a.params.pagination.bulletClass))&&i.click())}function m(){return a.pagination&&a.pagination.bullets&&a.pagination.bullets.length}function v(){return m()&&a.params.pagination.clickable}const E=(e,a,t)=>{o(e),"BUTTON"!==e.tagName&&(c(e,"button"),e.addEventListener("keydown",f)),p(e,t),function(e,a){(e=r(e)).forEach((e=>{e.setAttribute("aria-controls",a)}))}(e,a)},b=()=>{a.a11y.clicked=!0},h=()=>{requestAnimationFrame((()=>{requestAnimationFrame((()=>{a.destroyed||(a.a11y.clicked=!1)}))}))},y=e=>{if(a.a11y.clicked)return;const t=e.target.closest(`.${a.params.slideClass}, swiper-slide`);if(!t||!a.slides.includes(t))return;const i=a.slides.indexOf(t)===a.activeIndex,n=a.params.watchSlidesProgress&&a.visibleSlides&&a.visibleSlides.includes(t);i||n||e.sourceCapabilities&&e.sourceCapabilities.firesTouchEvents||(a.isHorizontal()?a.el.scrollLeft=0:a.el.scrollTop=0,a.slideTo(a.slides.indexOf(t),0))},M=()=>{const e=a.params.a11y;e.itemRoleDescriptionMessage&&d(a.slides,e.itemRoleDescriptionMessage),e.slideRole&&c(a.slides,e.slideRole);const t=a.slides.length;e.slideLabelMessage&&a.slides.forEach(((i,n)=>{const s=a.params.loop?parseInt(i.getAttribute("data-swiper-slide-index"),10):n;p(i,e.slideLabelMessage.replace(/\{\{index\}\}/,s+1).replace(/\{\{slidesLength\}\}/,t))}))},A=()=>{const e=a.params.a11y;a.el.append(n);const t=a.el;e.containerRoleDescriptionMessage&&d(t,e.containerRoleDescriptionMessage),e.containerMessage&&p(t,e.containerMessage);const i=a.wrapperEl,s=e.id||i.getAttribute("id")||`swiper-wrapper-${o=16,void 0===o&&(o=16),"x".repeat(o).replace(/x/g,(()=>Math.round(16*Math.random()).toString(16)))}`;var o;const l=a.params.autoplay&&a.params.autoplay.enabled?"off":"polite";var c;c=s,r(i).forEach((e=>{e.setAttribute("id",c)})),function(e,a){(e=r(e)).forEach((e=>{e.setAttribute("aria-live",a)}))}(i,l),M();let{nextEl:g,prevEl:u}=a.navigation?a.navigation:{};if(g=r(g),u=r(u),g&&g.forEach((a=>E(a,s,e.nextSlideMessage))),u&&u.forEach((a=>E(a,s,e.prevSlideMessage))),v()){(Array.isArray(a.pagination.el)?a.pagination.el:[a.pagination.el]).forEach((e=>{e.addEventListener("keydown",f)}))}a.el.addEventListener("focus",y,!0),a.el.addEventListener("pointerdown",b,!0),a.el.addEventListener("pointerup",h,!0)};i("beforeInit",(()=>{n=createElement("span",a.params.a11y.notificationClass),n.setAttribute("aria-live","assertive"),n.setAttribute("aria-atomic","true")})),i("afterInit",(()=>{a.params.a11y.enabled&&A()})),i("slidesLengthChange snapGridLengthChange slidesGridLengthChange",(()=>{a.params.a11y.enabled&&M()})),i("fromEdge toEdge afterInit lock unlock",(()=>{a.params.a11y.enabled&&function(){if(a.params.loop||a.params.rewind||!a.navigation)return;const{nextEl:e,prevEl:t}=a.navigation;t&&(a.isBeginning?(g(t),l(t)):(u(t),o(t))),e&&(a.isEnd?(g(e),l(e)):(u(e),o(e)))}()})),i("paginationUpdate",(()=>{a.params.a11y.enabled&&function(){const e=a.params.a11y;m()&&a.pagination.bullets.forEach((t=>{a.params.pagination.clickable&&(o(t),a.params.pagination.renderBullet||(c(t,"button"),p(t,e.paginationBulletMessage.replace(/\{\{index\}\}/,elementIndex(t)+1)))),t.matches(classesToSelector(a.params.pagination.bulletActiveClass))?t.setAttribute("aria-current","true"):t.removeAttribute("aria-current")}))}()})),i("destroy",(()=>{a.params.a11y.enabled&&function(){n&&n.remove();let{nextEl:e,prevEl:t}=a.navigation?a.navigation:{};e=r(e),t=r(t),e&&e.forEach((e=>e.removeEventListener("keydown",f))),t&&t.forEach((e=>e.removeEventListener("keydown",f))),v()&&(Array.isArray(a.pagination.el)?a.pagination.el:[a.pagination.el]).forEach((e=>{e.removeEventListener("keydown",f)}));a.el.removeEventListener("focus",y,!0),a.el.removeEventListener("pointerdown",b,!0),a.el.removeEventListener("pointerup",h,!0)}()}))}export{A11y as default};
//# sourceMappingURL=a11y.min.mjs.map