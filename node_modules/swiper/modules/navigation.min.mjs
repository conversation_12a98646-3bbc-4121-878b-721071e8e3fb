import{c as createElementIfNotDefined}from"../shared/create-element-if-not-defined.min.mjs";function Navigation(a){let{swiper:n,extendParams:e,on:i,emit:t}=a;e({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),n.navigation={nextEl:null,prevEl:null};const s=a=>(Array.isArray(a)?a:[a]).filter((a=>!!a));function l(a){let e;return a&&"string"==typeof a&&n.isElement&&(e=n.el.querySelector(a),e)?e:(a&&("string"==typeof a&&(e=[...document.querySelectorAll(a)]),n.params.uniqueNavElements&&"string"==typeof a&&e.length>1&&1===n.el.querySelectorAll(a).length&&(e=n.el.querySelector(a))),a&&!e?a:e)}function o(a,e){const i=n.params.navigation;(a=s(a)).forEach((a=>{a&&(a.classList[e?"add":"remove"](...i.disabledClass.split(" ")),"BUTTON"===a.tagName&&(a.disabled=e),n.params.watchOverflow&&n.enabled&&a.classList[n.isLocked?"add":"remove"](i.lockClass))}))}function r(){const{nextEl:a,prevEl:e}=n.navigation;if(n.params.loop)return o(e,!1),void o(a,!1);o(e,n.isBeginning&&!n.params.rewind),o(a,n.isEnd&&!n.params.rewind)}function d(a){a.preventDefault(),(!n.isBeginning||n.params.loop||n.params.rewind)&&(n.slidePrev(),t("navigationPrev"))}function c(a){a.preventDefault(),(!n.isEnd||n.params.loop||n.params.rewind)&&(n.slideNext(),t("navigationNext"))}function p(){const a=n.params.navigation;if(n.params.navigation=createElementIfNotDefined(n,n.originalParams.navigation,n.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!a.nextEl&&!a.prevEl)return;let e=l(a.nextEl),i=l(a.prevEl);Object.assign(n.navigation,{nextEl:e,prevEl:i}),e=s(e),i=s(i);const t=(e,i)=>{e&&e.addEventListener("click","next"===i?c:d),!n.enabled&&e&&e.classList.add(...a.lockClass.split(" "))};e.forEach((a=>t(a,"next"))),i.forEach((a=>t(a,"prev")))}function v(){let{nextEl:a,prevEl:e}=n.navigation;a=s(a),e=s(e);const i=(a,e)=>{a.removeEventListener("click","next"===e?c:d),a.classList.remove(...n.params.navigation.disabledClass.split(" "))};a.forEach((a=>i(a,"next"))),e.forEach((a=>i(a,"prev")))}i("init",(()=>{!1===n.params.navigation.enabled?g():(p(),r())})),i("toEdge fromEdge lock unlock",(()=>{r()})),i("destroy",(()=>{v()})),i("enable disable",(()=>{let{nextEl:a,prevEl:e}=n.navigation;a=s(a),e=s(e),n.enabled?r():[...a,...e].filter((a=>!!a)).forEach((a=>a.classList.add(n.params.navigation.lockClass)))})),i("click",((a,e)=>{let{nextEl:i,prevEl:l}=n.navigation;i=s(i),l=s(l);const o=e.target;if(n.params.navigation.hideOnClick&&!l.includes(o)&&!i.includes(o)){if(n.pagination&&n.params.pagination&&n.params.pagination.clickable&&(n.pagination.el===o||n.pagination.el.contains(o)))return;let a;i.length?a=i[0].classList.contains(n.params.navigation.hiddenClass):l.length&&(a=l[0].classList.contains(n.params.navigation.hiddenClass)),t(!0===a?"navigationShow":"navigationHide"),[...i,...l].filter((a=>!!a)).forEach((a=>a.classList.toggle(n.params.navigation.hiddenClass)))}}));const g=()=>{n.el.classList.add(...n.params.navigation.navigationDisabledClass.split(" ")),v()};Object.assign(n.navigation,{enable:()=>{n.el.classList.remove(...n.params.navigation.navigationDisabledClass.split(" ")),p(),r()},disable:g,update:r,init:p,destroy:v})}export{Navigation as default};
//# sourceMappingURL=navigation.min.mjs.map