function Grid(r){let e,i,a,s,{swiper:t,extendParams:l,on:o}=r;l({grid:{rows:1,fill:"column"}});const n=()=>{let r=t.params.spaceBetween;return"string"==typeof r&&r.indexOf("%")>=0?r=parseFloat(r.replace("%",""))/100*t.size:"string"==typeof r&&(r=parseFloat(r)),r};o("init",(()=>{s=t.params.grid&&t.params.grid.rows>1})),o("update",(()=>{const{params:r,el:e}=t,i=r.grid&&r.grid.rows>1;s&&!i?(e.classList.remove(`${r.containerModifierClass}grid`,`${r.containerModifierClass}grid-column`),a=1,t.emitContainerClasses()):!s&&i&&(e.classList.add(`${r.containerModifierClass}grid`),"column"===r.grid.fill&&e.classList.add(`${r.containerModifierClass}grid-column`),t.emitContainerClasses()),s=i})),t.grid={initSlides:r=>{const{slidesPerView:s}=t.params,{rows:l,fill:o}=t.params.grid;a=Math.floor(r/l),e=Math.floor(r/l)===r/l?r:Math.ceil(r/l)*l,"auto"!==s&&"row"===o&&(e=Math.max(e,s*l)),i=e/l},updateSlide:(r,s,l,o)=>{const{slidesPerGroup:d}=t.params,p=n(),{rows:c,fill:f}=t.params.grid;let m,u,g;if("row"===f&&d>1){const i=Math.floor(r/(d*c)),a=r-c*d*i,t=0===i?d:Math.min(Math.ceil((l-i*c*d)/c),d);g=Math.floor(a/t),u=a-g*t+i*d,m=u+g*e/c,s.style.order=m}else"column"===f?(u=Math.floor(r/c),g=r-u*c,(u>a||u===a&&g===c-1)&&(g+=1,g>=c&&(g=0,u+=1))):(g=Math.floor(r/i),u=r-g*i);s.row=g,s.column=u,s.style[o("margin-top")]=0!==g?p&&`${p}px`:""},updateWrapperSize:(r,i,a)=>{const{centeredSlides:s,roundLengths:l}=t.params,o=n(),{rows:d}=t.params.grid;if(t.virtualSize=(r+o)*e,t.virtualSize=Math.ceil(t.virtualSize/d)-o,t.wrapperEl.style[a("width")]=`${t.virtualSize+o}px`,s){const r=[];for(let e=0;e<i.length;e+=1){let a=i[e];l&&(a=Math.floor(a)),i[e]<t.virtualSize+i[0]&&r.push(a)}i.splice(0,i.length),i.push(...r)}}}}export{Grid as default};
//# sourceMappingURL=grid.min.mjs.map