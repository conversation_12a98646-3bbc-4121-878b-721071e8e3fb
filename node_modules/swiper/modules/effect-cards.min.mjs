import{c as createShadow}from"../shared/create-shadow.min.mjs";import{e as effectInit}from"../shared/effect-init.min.mjs";import{e as effectTarget}from"../shared/effect-target.min.mjs";import{e as effectVirtualTransitionEnd}from"../shared/effect-virtual-transition-end.min.mjs";import{k as getSlideTransformEl}from"../shared/utils.min.mjs";function EffectCards(e){let{swiper:t,extendParams:a,on:s}=e;a({cardsEffect:{slideShadows:!0,rotate:!0,perSlideRotate:2,perSlideOffset:8}});effectInit({effect:"cards",swiper:t,on:s,setTranslate:()=>{const{slides:e,activeIndex:a,rtlTranslate:s}=t,r=t.params.cardsEffect,{startTranslate:i,isTouched:n}=t.touchEventsData,o=s?-t.translate:t.translate;for(let l=0;l<e.length;l+=1){const d=e[l],f=d.progress,c=Math.min(Math.max(f,-4),4);let m=d.swiperSlideOffset;t.params.centeredSlides&&!t.params.cssMode&&(t.wrapperEl.style.transform=`translateX(${t.minTranslate()}px)`),t.params.centeredSlides&&t.params.cssMode&&(m-=e[0].swiperSlideOffset);let p=t.params.cssMode?-m-t.translate:-m,h=0;const M=-100*Math.abs(c);let u=1,w=-r.perSlideRotate*c,S=r.perSlideOffset-.75*Math.abs(c);const $=t.virtual&&t.params.virtual.enabled?t.virtual.from+l:l,E=($===a||$===a-1)&&c>0&&c<1&&(n||t.params.cssMode)&&o<i,T=($===a||$===a+1)&&c<0&&c>-1&&(n||t.params.cssMode)&&o>i;if(E||T){const e=(1-Math.abs((Math.abs(c)-.5)/.5))**.5;w+=-28*c*e,u+=-.5*e,S+=96*e,h=-25*e*Math.abs(c)+"%"}if(p=c<0?`calc(${p}px ${s?"-":"+"} (${S*Math.abs(c)}%))`:c>0?`calc(${p}px ${s?"-":"+"} (-${S*Math.abs(c)}%))`:`${p}px`,!t.isHorizontal()){const e=h;h=p,p=e}const x=c<0?""+(1+(1-u)*c):""+(1-(1-u)*c),b=`\n        translate3d(${p}, ${h}, ${M}px)\n        rotateZ(${r.rotate?s?-w:w:0}deg)\n        scale(${x})\n      `;if(r.slideShadows){let e=d.querySelector(".swiper-slide-shadow");e||(e=createShadow("cards",d)),e&&(e.style.opacity=Math.min(Math.max((Math.abs(c)-.5)/.5,0),1))}d.style.zIndex=-Math.abs(Math.round(f))+e.length;effectTarget(r,d).style.transform=b}},setTransition:e=>{const a=t.slides.map((e=>getSlideTransformEl(e)));a.forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow").forEach((t=>{t.style.transitionDuration=`${e}ms`}))})),effectVirtualTransitionEnd({swiper:t,duration:e,transformElements:a})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!t.params.cssMode})})}export{EffectCards as default};
//# sourceMappingURL=effect-cards.min.mjs.map