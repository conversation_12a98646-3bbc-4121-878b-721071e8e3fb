{"version": 3, "file": "ssr-window.esm.mjs.mjs", "names": ["isObject", "obj", "constructor", "Object", "extend", "target", "src", "keys", "for<PERSON>ach", "key", "length", "ssrDocument", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "createElementNS", "importNode", "location", "hash", "host", "hostname", "href", "origin", "pathname", "protocol", "search", "getDocument", "doc", "document", "ssrWindow", "navigator", "userAgent", "history", "replaceState", "pushState", "go", "back", "CustomEvent", "this", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "matchMedia", "requestAnimationFrame", "callback", "cancelAnimationFrame", "id", "getWindow", "win", "window"], "sources": ["0"], "mappings": "AAYA,SAASA,SAASC,GAChB,OAAe,OAARA,GAA+B,iBAARA,GAAoB,gBAAiBA,GAAOA,EAAIC,cAAgBC,MAChG,CACA,SAASC,OAAOC,EAAQC,QACP,IAAXD,IACFA,EAAS,CAAC,QAEA,IAARC,IACFA,EAAM,CAAC,GAETH,OAAOI,KAAKD,GAAKE,SAAQC,SACI,IAAhBJ,EAAOI,GAAsBJ,EAAOI,GAAOH,EAAIG,GAAcT,SAASM,EAAIG,KAAST,SAASK,EAAOI,KAASN,OAAOI,KAAKD,EAAIG,IAAMC,OAAS,GACpJN,OAAOC,EAAOI,GAAMH,EAAIG,GAC1B,GAEJ,CACA,MAAME,YAAc,CAClBC,KAAM,CAAC,EACPC,mBAAoB,EACpBC,sBAAuB,EACvBC,cAAe,CACbC,OAAQ,EACRC,SAAU,IAEZC,cAAa,IACJ,KAETC,iBAAgB,IACP,GAETC,eAAc,IACL,KAETC,YAAW,KACF,CACLC,YAAa,IAGjBC,cAAa,KACJ,CACLC,SAAU,GACVC,WAAY,GACZC,MAAO,CAAC,EACRC,eAAgB,EAChBC,qBAAoB,IACX,KAIbC,gBAAe,KACN,CAAC,GAEVC,WAAU,IACD,KAETC,SAAU,CACRC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,KAGZ,SAASC,cACP,MAAMC,EAA0B,oBAAbC,SAA2BA,SAAW,CAAC,EAE1D,OADAtC,OAAOqC,EAAK9B,aACL8B,CACT,CACA,MAAME,UAAY,CAChBD,SAAU/B,YACViC,UAAW,CACTC,UAAW,IAEbd,SAAU,CACRC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,IAEVO,QAAS,CACPC,eAAgB,EAChBC,YAAa,EACbC,KAAM,EACNC,OAAQ,GAEVC,YAAa,WACX,OAAOC,IACT,EACAvC,mBAAoB,EACpBC,sBAAuB,EACvBuC,iBAAgB,KACP,CACLC,iBAAgB,IACP,KAIbC,QAAS,EACTC,OAAQ,EACRC,OAAQ,CAAC,EACTC,aAAc,EACdC,eAAgB,EAChBC,WAAU,KACD,CAAC,GAEVC,sBAAsBC,GACM,oBAAfJ,YACTI,IACO,MAEFJ,WAAWI,EAAU,GAE9BC,qBAAqBC,GACO,oBAAfN,YAGXC,aAAaK,EACf,GAEF,SAASC,YACP,MAAMC,EAAwB,oBAAXC,OAAyBA,OAAS,CAAC,EAEtD,OADA/D,OAAO8D,EAAKvB,WACLuB,CACT,QAESD,eAAgBzB"}