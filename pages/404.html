<div class="page" data-name="not-found">
  <div class="navbar">
    <div class="navbar-bg"></div>
    <div class="navbar-inner sliding">
      <div class="left">
        <a href="/" class="link">
          <i class="icon icon-back"></i>
          <span class="if-not-md">Home</span>
        </a>
      </div>
      <div class="title">Page Not Found</div>
    </div>
  </div>

  <div class="page-content">
    <div class="block text-align-center" style="padding: 80px 20px;">
      <i class="f7-icons" style="font-size: 120px; color: #ccc; margin-bottom: 30px;">exclamationmark_triangle</i>
      
      <h1 style="color: #666; margin-bottom: 15px;">404</h1>
      <h2 style="color: #888; margin-bottom: 20px;">Page Not Found</h2>
      
      <p style="color: #999; margin-bottom: 40px; font-size: 16px;">
        The page you're looking for doesn't exist or has been moved.
      </p>
      
      <div class="row">
        <div class="col-50">
          <a href="/" class="button button-fill button-large color-blue">
            <i class="icon f7-icons">house</i> Go Home
          </a>
        </div>
        <div class="col-50">
          <a href="/news/" class="button button-fill button-large color-green">
            <i class="icon f7-icons">news</i> Browse News
          </a>
        </div>
      </div>
    </div>

    <!-- Suggested Pages -->
    <div class="block-title">You might be looking for:</div>
    <div class="list">
      <ul>
        <li>
          <a href="/" class="item-link item-content">
            <div class="item-media">
              <i class="icon f7-icons color-blue">house_fill</i>
            </div>
            <div class="item-inner">
              <div class="item-title">Home</div>
              <div class="item-subtitle">Dashboard and featured content</div>
            </div>
          </a>
        </li>
        <li>
          <a href="/news/" class="item-link item-content">
            <div class="item-media">
              <i class="icon f7-icons color-green">news</i>
            </div>
            <div class="item-inner">
              <div class="item-title">Latest News</div>
              <div class="item-subtitle">Browse all news articles</div>
            </div>
          </a>
        </li>
        <li>
          <a href="/profile/" class="item-link item-content">
            <div class="item-media">
              <i class="icon f7-icons color-orange">person_fill</i>
            </div>
            <div class="item-inner">
              <div class="item-title">Profile</div>
              <div class="item-subtitle">Your account and preferences</div>
            </div>
          </a>
        </li>
        <li>
          <a href="/settings/" class="item-link item-content">
            <div class="item-media">
              <i class="icon f7-icons color-gray">gear</i>
            </div>
            <div class="item-inner">
              <div class="item-title">Settings</div>
              <div class="item-subtitle">App configuration and preferences</div>
            </div>
          </a>
        </li>
      </ul>
    </div>

    <!-- Help Section -->
    <div class="block-title">Need Help?</div>
    <div class="block">
      <p style="color: #666; margin-bottom: 20px;">
        If you think this is an error, you can:
      </p>
      <ul style="color: #888; padding-left: 20px;">
        <li>Check the URL for typos</li>
        <li>Use the navigation menu to find what you're looking for</li>
        <li>Go back to the previous page</li>
        <li>Contact support if the problem persists</li>
      </ul>
    </div>

    <!-- Back Button -->
    <div class="block">
      <a href="#" onclick="history.back()" class="button button-large">
        <i class="icon f7-icons">arrow_left</i> Go Back
      </a>
    </div>
  </div>
</div>
