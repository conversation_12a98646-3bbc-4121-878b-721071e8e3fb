<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="theme-color" content="#007aff">
  
  <!-- PWA Meta Tags -->
  <link rel="manifest" href="manifest.json">
  <link rel="apple-touch-icon" href="icons/icon-192.png">
  
  <title>MobileNews - Framework7 Demo</title>
  
  <!-- Framework7 CSS -->
  <link rel="stylesheet" href="node_modules/framework7/framework7-bundle.min.css">
  
  <!-- Custom CSS -->
  <link rel="stylesheet" href="css/app.css">
</head>
<body>
  <!-- App root element -->
  <div id="app">
    
    <!-- Status bar overlay for fullscreen mode-->
    <div class="statusbar"></div>

    <!-- Your main view, should have "view-main" class -->
    <div class="view view-main view-init" data-master-detail-breakpoint="800">
      
      <!-- Top Navbar -->
      <div class="navbar navbar-large">
        <div class="navbar-bg"></div>
        <div class="navbar-inner sliding">
          <div class="left">
            <a href="#" class="link icon-only panel-open" data-panel="left">
              <i class="icon f7-icons">menu</i>
            </a>
          </div>
          <div class="title sliding">MobileNews</div>
          <div class="right">
            <a href="#" class="link icon-only">
              <i class="icon f7-icons">search</i>
            </a>
          </div>
          <div class="title-large">
            <div class="title-large-text">MobileNews</div>
          </div>
        </div>
      </div>
      
      <!-- Toolbar -->
      <div class="toolbar tabbar-labels toolbar-bottom">
        <div class="toolbar-inner">
          <a href="/" class="tab-link tab-link-active">
            <i class="icon f7-icons">house_fill</i>
            <span class="tabbar-label">Home</span>
          </a>
          <a href="/news/" class="tab-link">
            <i class="icon f7-icons">news</i>
            <span class="tabbar-label">News</span>
          </a>
          <a href="/profile/" class="tab-link">
            <i class="icon f7-icons">person_fill</i>
            <span class="tabbar-label">Profile</span>
          </a>
          <a href="/settings/" class="tab-link">
            <i class="icon f7-icons">gear</i>
            <span class="tabbar-label">Settings</span>
          </a>
        </div>
      </div>

      <!-- Pages container -->
      <div class="pages">
        <!-- Home page will be loaded here by router -->
      </div>
    </div>

    <!-- Left panel with cover effect-->
    <div class="panel panel-left panel-cover panel-init">
      <div class="view">
        <div class="page">
          <div class="navbar">
            <div class="navbar-bg"></div>
            <div class="navbar-inner">
              <div class="title">Menu</div>
            </div>
          </div>
          <div class="page-content">
            <div class="list">
              <ul>
                <li><a href="/" class="item-link panel-close"><div class="item-content"><div class="item-inner"><div class="item-title">Home</div></div></div></a></li>
                <li><a href="/news/" class="item-link panel-close"><div class="item-content"><div class="item-inner"><div class="item-title">News</div></div></div></a></li>
                <li><a href="/profile/" class="item-link panel-close"><div class="item-content"><div class="item-inner"><div class="item-title">Profile</div></div></div></a></li>
                <li><a href="/settings/" class="item-link panel-close"><div class="item-content"><div class="item-inner"><div class="item-title">Settings</div></div></div></a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Framework7 library -->
  <script src="node_modules/framework7/framework7-bundle.min.js"></script>
  
  <!-- App scripts -->
  <script src="js/data.js"></script>
  <script src="js/routes.js"></script>
  <script src="js/app.js"></script>
</body>
</html>
